# AI 规则

在 JoyCoder 中使用项目特定和全局规则自定义 AI 行为的指南

使用 JoyCoder 中的规则，您可以控制底层模型的行为。您可以将其视为 LLM 的指令和/或系统提示。

在 JoyCoder 中，我们有两种主要方式来根据您的需求自定义 AI 的行为：

## 项目规则

特定于项目的规则，存储在 .joycoder/rules 目录中。当引用匹配的文件时，它们会自动包含在内。

## 全局规则

应用于所有项目的全局规则，在 JoyCoder 设置 > AI 规则 部分进行配置。

在以下部分中了解更多关于如何使用它们的信息。

​

## 项目规则（推荐）

项目规则提供了一个强大而灵活的系统，具有路径特定的配置。项目规则存储在 .joycoder/rules 目录中，并提供对项目不同部分的 AI 行为的精细控制。

以下是它们的工作原理：

- 语义描述：每个规则可以包含应用时的描述
- 文件模式匹配：使用 glob 模式指定规则适用的文件/文件夹
- 自动附加：当引用匹配的文件时，规则可以自动包含
- 引用文件：在项目规则中使用 @file 来包含它们作为应用规则时的上下文
- 您可以使用 @file 引用规则文件，允许您将多个规则链接在一起

示例用例：

- 特定文件类型的框架特定规则（例如，.tsx 文件的 SolidJS 偏好）
- 自动生成文件的特殊处理（例如，.proto 文件）
- 自定义 UI 开发模式
- 特定文件夹的代码风格和架构偏好

​

## 全局规则

可以通过修改 JoyCoder 设置 > AI 规则 部分下的 AI 规则来添加全局规则。如果您想指定在每个项目中都应该包含的规则，如输出语言、响应长度等，这很有用。

​

## .joycoderules

为了向后兼容，您仍然可以在项目根目录中使用 .joycoderules 文件。我们最终会在将来移除 .joycoderules，因此我们建议迁移到新的项目规则系统以获得更好的灵活性和控制。
