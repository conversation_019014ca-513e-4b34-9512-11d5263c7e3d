# JoyCoder API

The JoyCode extension exposes an API that can be used by other extensions. To use this API in your extension:

1. Copy `src/extension-api/joycoder.d.ts` to your extension's source directory.
2. Include `joycoder.d.ts` in your extension's compilation.
3. Get access to the API with the following code:

    ```ts
    const joycoderExtension = vscode.extensions.getExtension<JoyCoderAPI>("saoudrizwan.claude-dev")

    if (!joycoderExtension?.isActive) {
    	throw new Error("JoyCode extension is not activated")
    }

    const joycoder = joycoderExtension.exports

    if (joycoder) {
    	// Now you can use the API

    	// Set custom instructions
    	await joycoder.setCustomInstructions("Talk like a pirate")

    	// Get custom instructions
    	const instructions = await joycoder.getCustomInstructions()
    	console.log("Current custom instructions:", instructions)

    	// Start a new task with an initial message
    	await joycoder.startNewTask("Hello, JoyCode! Let's make a new project...")

    	// Start a new task with an initial message and images
    	await joycoder.startNewTask("Use this design language", ["data:image/webp;base64,..."])

    	// Send a message to the current task
    	await joycoder.sendMessage("Can you fix the @problems?")

    	// Simulate pressing the primary button in the chat interface (e.g. 'Save' or 'Proceed While Running')
    	await joycoder.pressPrimaryButton()

    	// Simulate pressing the secondary button in the chat interface (e.g. 'Reject')
    	await joycoder.pressSecondaryButton()
    } else {
    	console.error("JoyCode API is not available")
    }
    ```

    **Note:** To ensure that the `saoudrizwan.claude-dev` extension is activated before your extension, add it to the `extensionDependencies` in your `package.json`:

    ```json
    "extensionDependencies": [
        "saoudrizwan.claude-dev"
    ]
    ```

For detailed information on the available methods and their usage, refer to the `joycoder.d.ts` file.
