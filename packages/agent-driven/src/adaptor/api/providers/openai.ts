import { sm2 } from 'sm-crypto';
import { Anthropic } from '@anthropic-ai/sdk';
import OpenAI from 'openai';
import { ApiHandlerOptions, ModelInfo, openAiModelInfoSaneDefaults } from '../../../shared/api';
import { ApiHandler } from '../index';
import { convertToOpenAiMessages } from '../transform/openai-format';
import { ApiStream } from '../transform/stream';
import { getExtParams, checkLogin, getExtHeaders } from '@joycoder/agent-common/src/vscode/index';
import { getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { getJdhLoginInfo, WorkspaceState, isIDE, getBaseUrl } from '@joycoder/shared';
import { oneKeyCreateModeInstructions } from '../../../core/prompts/instructions/one-key-create-mode';
import { getMaxOutputTokens } from '../../../shared/modelConfig';

const errorMessage = (errorJson: any) => {
  let modifiedError = '';
  // 上下文超长类，不同大模型返回结果不同，有一部分大模型返回的错误码为9002，与请求数过多的错误码一致，优先判断处理上下文超长
  if (
    errorJson.error?.message?.includes(`This model's maximum context length`) || //GPT-4-omni
    errorJson.error?.message?.includes(`is longer than the model's context length`) || //DeepSeekR1
    errorJson.error?.message?.includes(`Input is too long for requested model`) || //Claude3.5
    errorJson.error?.message?.includes(`too many images and documents`) || //Claude3.7
    errorJson.error?.message?.includes(`exceed context limit`) || //Claude3.7
    errorJson.error?.message?.includes("Requested token count exceeds the model's maximum context length") || //DeepSeekV3
    errorJson.error?.code === 'context_length_exceeded' //GPT-4-omni
  ) {
    modifiedError = '上下文输出超长，请减少输入长度后重试！';
    // modifiedError = '上下文输出超长。点击重试以截断对话并再次尝试。';
  } else if (errorJson.error?.message?.includes('tokens limit for minute')) {
    //Gemini-2.5-pro
    modifiedError = '当前模型调用量已超过阈值，请稍后再试！';
  } else if (errorJson.error?.code == 3001) {
    modifiedError = '调用模型服务超时，请稍后再试！';
  } else if (errorJson.error?.code == 3002) {
    modifiedError = '当前调用量已超过阈值，请稍后再试！';
  } else if (errorJson.error?.code == 3004) {
    modifiedError = '调用触发限流，请稍后再试！';
  } else if (errorJson.error?.code == 9002 && errorJson.error?.message?.includes('image exceeds 5 MB maximum')) {
    modifiedError = '上传图片过大（超过5MB），请减小图片大小后重试！';
  } else if (errorJson.error?.code == 9002) {
    //9002还有一部分错误为上下文超长，已经提前处理
    modifiedError = '当前请求数过多，请稍后再试！';
  } else if (errorJson.error?.code == 1033 || errorJson.error?.message?.includes(`敏感词`)) {
    modifiedError = `当前输入中包含邮箱、手机号、密码等敏感信息，请检查后重试！${errorJson.error?.message ?? ''}`;
  }
  //以下为旧的处理逻辑
  else if (errorJson.error?.message?.includes('Model is getting throttled.')) {
    modifiedError = '模型正在被限流，请稍后重试！';
  } else if (errorJson.error?.code == 1050) {
    modifiedError = `${errorJson.error?.message ?? ''}`;
  } else if (errorJson.error?.code == 13 || errorJson.error?.message?.includes(`未登录`)) {
    modifiedError = '未登录，请点击<a class="joycoder-login-btn">去浏览器授权</a>登录，登录后重试！';
  } else if (isIDE() && errorJson?.code == 401) {
    modifiedError = '未登录，请点击<a class="joycoder-login-btn">去浏览器授权</a>登录，登录后重试！';
  } else {
    modifiedError = '异常: 模型请求失败，请切换模型重试！';
  }
  return modifiedError;
};
// 自定义 fetch 函数
const customFetch: typeof fetch = async (input, init) => {
  let response;
  if (isIDE()) {
    const extHeaders = getExtHeaders().headers;
    const headers: Record<string, string> = {
      'Content-Type': extHeaders['Content-Type'],
    };

    if ('ptKey' in extHeaders && extHeaders.ptKey) {
      headers.ptKey = extHeaders.ptKey;
    }

    const finalHeaders = {
      ...headers,
    };

    response = await fetch(input, {
      method: 'POST',
      headers: finalHeaders,
      body: init?.body,
    });
  } else {
    response = await fetch(input, init);
  }

  // 检查是否为流式响应
  if (response.headers.get('content-type')?.includes('text/event-stream')) {
    const reader = response.body!.getReader();
    const stream = new ReadableStream({
      async start(controller) {
        try {
          let fullText = '';
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const text = new TextDecoder().decode(value);

            if (isIDE() && text.includes('401') && text.includes('账号未登录')) {
              try {
                let errorJson = typeof text == 'string' ? JSON.parse(text) : text;
                if (errorJson.code === 401) {
                  let modifiedError = errorMessage(errorJson);
                  errorJson.error = {
                    code: '13',
                    message: modifiedError,
                  };
                  const modifiedData = JSON.stringify(errorJson);
                  controller.enqueue(new TextEncoder().encode(`data: ${modifiedData}\n\n`));
                  return;
                }
              } catch {}
            } else {
              // 定义响应类型
              interface StreamResponse {
                error?: {
                  code?: string;
                  message?: string;
                };
                success?: boolean;
                code?: string;
              }

              // 工具函数：将数据写入响应流
              const enqueueData = (data: any) => {
                controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(data)}\n\n`));
              };

              // 处理错误响应
              const handleError = (parsed: StreamResponse): boolean => {
                // 处理标准错误
                if (parsed.error) {
                  const modifiedError = errorMessage(parsed);
                  parsed.error.message = modifiedError;
                  enqueueData(parsed);
                  return true;
                }

                // 处理未授权错误
                if (parsed.success === false && parsed.code === '1008') {
                  enqueueData({
                    error: {
                      code: '13',
                      message: '未登录，请点击<a class="joycoder-login-btn">去浏览器授权</a>登录，登录后重试！',
                    },
                  });
                  return true;
                }

                return false;
              };

              // 解析JSON
              const parseJSON = (text: string): StreamResponse | null => {
                try {
                  return JSON.parse(text);
                } catch {
                  return null;
                }
              };

              // 处理单行数据
              const processLine = (line: string): boolean => {
                // 提取并清理数据内容
                const stripDataPrefix = (text: string): string => {
                  let content = text.trim();
                  const DATA_PREFIX = /^data:\s*/;
                  while (DATA_PREFIX.test(content)) {
                    content = content.replace(DATA_PREFIX, '').trim();
                  }
                  return content;
                };

                const content = stripDataPrefix(line);

                // 检查特殊情况
                if (!content || content === '[DONE]') {
                  return false;
                }

                // 尝试解析JSON
                const tryParseAndHandle = (jsonStr: string): boolean => {
                  const parsed = parseJSON(jsonStr);
                  if (parsed) {
                    fullText = '';
                    return handleError(parsed);
                  }
                  return false;
                };

                // 先尝试解析当前内容
                if (tryParseAndHandle(content)) {
                  return true;
                }

                // 累积并尝试解析
                fullText += content;
                return tryParseAndHandle(fullText);
              };

              // 主处理逻辑
              const lines = text.split('\n');
              let shouldReturn = false;

              for (const line of lines) {
                if (processLine(line)) {
                  shouldReturn = true;
                  break;
                }
              }

              if (!shouldReturn) {
                controller.enqueue(value);
              }
            }
          }
        } catch (error) {
          console.warn('%c [ error ]-55', 'font-size:13px; background:pink; color:#bf2c9f;', error);
          controller.error(error);
        } finally {
          controller.close();
        }
      },
    });

    return new Response(stream, {
      headers: response.headers,
      status: response.status,
      statusText: response.statusText,
    });
  } else if (!response.ok) {
    // 对于非流式响应，我们检查响应内容是否包含错误
    const errorText = response.headers.get('content-type')?.includes('application/json')
      ? await response.json()
      : await response.text();
    try {
      const errorJson = typeof errorText == 'string' ? JSON.parse(errorText) : errorText;
      if (errorJson.error) {
        let modifiedError = errorMessage(errorJson);
        errorJson.error.message = modifiedError;
        return new Response(JSON.stringify(errorJson), {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
        });
      }
    } catch (e) {
      // 如果不是有效的 JSON，则返回原始错误文本
      return new Response(e.message || '错误: 模型请求失败，请切换模型重试！', {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      });
    }
  }

  return response;
};

export class OpenAiHandler implements ApiHandler {
  private client: OpenAI;
  options: ApiHandlerOptions;

  constructor(options: ApiHandlerOptions) {
    checkLogin();
    let BASE_URL = 'http://jdhgpt.jd.com/open/v1/';
    if (isIDE()) {
      BASE_URL = getBaseUrl() + '/api/saas/openai/v1/';
    }
    this.options = options;
    this.client = new OpenAI({
      baseURL: BASE_URL,
      apiKey: 'empty',
      fetch: customFetch,
    });
  }

  async *createMessage(systemPrompt: string, messages: Anthropic.Messages.MessageParam[]): ApiStream {
    const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
      { role: 'system', content: systemPrompt },
      ...convertToOpenAiMessages(messages),
    ];
    const label = WorkspaceState.get('openAiModelId');
    const modelConfig = getChatModelAndConfig(label);
    // try {
    let reqBody: any = {
      model: modelConfig.chatApiModel ?? '',
      max_tokens: getMaxOutputTokens(),
      messages: openAiMessages,
      temperature: modelConfig.temperature ?? 0,
      stream: modelConfig.stream ?? true,
    };
    if (isIDE()) {
      const encryptData = sm2.doEncrypt(JSON.stringify(reqBody), getJdhLoginInfo()?.pk ?? '', 1);
      reqBody = {
        ...reqBody,
        enData: encryptData,
      };
    } else {
      reqBody = {
        ...reqBody,
        ...getExtParams()?.jdhLoginParams,
      };
    }

    const stream = await this.client.chat.completions.create(reqBody as any);

    //@ts-ignore
    for await (const chunk of stream) {
      try {
        const delta = chunk?.choices?.[0]?.delta;
        if (delta?.content) {
          yield {
            type: 'text',
            text: delta.content,
            conversationId: chunk?.id || '',
          };
        }
        if (delta && 'reasoning_content' in delta && delta.reasoning_content) {
          yield {
            type: 'reasoning',
            reasoning: (delta.reasoning_content as string | undefined) || '',
            reasoning_content: (delta.reasoning_content as string | undefined) || '',
          };
        }
        if (chunk.usage) {
          yield {
            type: 'usage',
            inputTokens: chunk.usage.prompt_tokens || 0,
            outputTokens: chunk.usage.completion_tokens || 0,
            conversationId: chunk?.id || '',
          };
        }
      } catch (parseError) {
        console.error('Error processing chunk:', parseError);
        console.error('Problematic chunk:', chunk);
      }
    }
    // } catch (error) {
    //   if (error.code == 13) {
    //     throw new Error(error?.message ?? '模型请求失败，请稍后重试或者切换模型重试！');
    //   }
    //   console.error('%c [ createMessage->error ]-65', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    //   throw new Error(error?.message ?? '模型请求失败，请稍后重试或者切换模型重试！');
    // }
  }

  getModel(): { id: string; info: ModelInfo } {
    return {
      id: this.options.openAiModelId ?? '',
      info: openAiModelInfoSaneDefaults,
    };
  }
  async completePrompt(prompt: string): Promise<string> {
    try {
      const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [{ role: 'user', content: prompt }];
      const label = WorkspaceState.get('openAiModelId');
      const modelConfig = getChatModelAndConfig(label);
      let reqBody: any = {
        model: modelConfig.chatApiModel ?? '',
        max_tokens: 4096,
        messages: openAiMessages,
        temperature: modelConfig.temperature ?? 0,
        stream: false,
      };
      if (isIDE()) {
        const encryptData = sm2.doEncrypt(JSON.stringify(reqBody), getJdhLoginInfo()?.pk ?? '', 1);
        reqBody = {
          ...reqBody,
          enData: encryptData,
        };
      } else {
        reqBody = {
          ...reqBody,
          ...getExtParams()?.jdhLoginParams,
        };
      }
      const response = await this.client.chat.completions.create(reqBody);
      return response.choices[0]?.message.content || '';
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`接口错误: ${error.message}`);
      }
      throw error;
    }
  }
  async completeAgent(prompt: string): Promise<string> {
    try {
      const systemPrompt = await oneKeyCreateModeInstructions();
      const openAiMessages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: prompt },
      ];
      const label = WorkspaceState.get('openAiModelId');
      const modelConfig = getChatModelAndConfig(label);
      let reqBody: any = {
        model: modelConfig.chatApiModel ?? '',
        max_tokens: 4096,
        messages: openAiMessages,
        temperature: modelConfig.temperature ?? 0,
        stream: false,
      };
      if (isIDE()) {
        const encryptData = sm2.doEncrypt(JSON.stringify(reqBody), getJdhLoginInfo()?.pk ?? '', 1);
        reqBody = {
          ...reqBody,
          enData: encryptData,
        };
      } else {
        reqBody = {
          ...reqBody,
          ...getExtParams()?.jdhLoginParams,
        };
      }
      const response = await this.client.chat.completions.create(reqBody);
      return response.choices[0]?.message.content || '';
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`接口错误: ${error.message}`);
      }
      throw error;
    }
  }
}
