import * as vscode from 'vscode';
import { constant, RightMenuChatType } from '@joycoder/plugin-base-ai/src/dialog/constant';
import { JoyCoderProvider } from '../../core/webview/JoycoderProvider';
import { Logger } from '@joycoder/shared';

interface IOptions {
  type: string;
  text: string;
  data?: any;
}
export async function askCoderByContextMenu(options: IOptions, coderProvider: JoyCoderProvider) {
  try {
    if (!options || !options.data?.functionContent) {
      Logger.showInformationMessage('请先选中要操作的代码块~');
      return;
    }
    await vscode.commands.executeCommand('joycoder.joycoder.SidebarProvider.focus');
    const contextMenuInstructions = constant(options.type, options);
    await coderProvider.updateGlobalState('contextMenuInstructions', contextMenuInstructions);
    const isSwitchMode =
      options.type === RightMenuChatType.codeUnitTest ||
      options.type === RightMenuChatType.codeReconstruction ||
      options.type === RightMenuChatType.codeWhyThrowErr;
    if (isSwitchMode) {
      coderProvider.handleModeSwitch('code');
    } else {
      coderProvider.handleModeSwitch('chat');
    }
    vscode.commands.executeCommand('joycoder.Coder.plusButtonClicked');
    const context = options.text ?? '';
    coderProvider.initJoyCoderWithTask(context);
  } catch (error) {
    console.warn('%c [ error ]-28', 'font-size:13px; background:pink; color:#bf2c9f;', error);
  }
}
