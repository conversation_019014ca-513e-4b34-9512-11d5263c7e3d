// @ts-nocheck
module.exports = {
  env: { language: 'en' },
  Uri: { file: (e) => ({ fsPath: e }), parse: (e) => ({ fsPath: e.replace('file://', '') }) },
  workspace: {
    getConfiguration: () => ({ get: (e) => ({ 'joycoder.browserViewportSize': '1280x800' }[e]), update: jest.fn() }),
  },
  window: {
    showInformationMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    createOutputChannel: () => ({
      appendLine: jest.fn(),
      append: jest.fn(),
      show: jest.fn(),
      clear: jest.fn(),
      dispose: jest.fn(),
    }),
  },
  commands: { registerCommand: jest.fn(), executeCommand: jest.fn() },
  ExtensionContext: class {
    constructor() {
      (this.subscriptions = []),
        (this.extensionPath = '/mock/extension/path'),
        (this.globalStoragePath = '/mock/storage/path'),
        (this.storagePath = '/mock/storage/path'),
        (this.logPath = '/mock/log/path'),
        (this.extensionUri = { fsPath: '/mock/extension/path' }),
        (this.globalStorageUri = { fsPath: '/mock/settings/path' }),
        (this.workspaceState = { get: () => {}, update: () => Promise.resolve() }),
        (this.globalState = { get: () => {}, update: () => Promise.resolve(), setKeysForSync: () => {} }),
        (this.asAbsolutePath = (e) => `/mock/extension/path/${e}`),
        (this.extension = { packageJSON: { version: '1.0.0' } });
    }
  },
};
