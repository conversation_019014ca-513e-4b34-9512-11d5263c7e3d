import fs from 'fs/promises';
import { globby } from 'globby';
import * as path from 'path';
import simpleGit, { SimpleGit } from 'simple-git';
import { fileExistsAtPath } from '../../utils/fs';
import { getLfsPatterns, writeExcludesFile } from './CheckpointExclusions';
import { DEFAULT_IGNORE_DIRS } from '@joycoder/shared';

interface CheckpointAddResult {
  success: boolean;
}

/**
 * GitOperations Class
 *
 * Handles git-specific operations for JoyCoder's Checkpoints system.
 *
 * Key responsibilities:
 * - Git repository initialization and configuration
 * - Git settings management (user, LFS, etc.)
 * - Worktree configuration and management
 * - Managing nested git repositories during checkpoint operations
 * - File staging and checkpoint creation
 * - Shadow git repository maintenance and cleanup
 */
export class GitOperations {
  private cwd: string;

  /**
   * Creates a new GitOperations instance.
   *
   * @param cwd - The current working directory for git operations
   */
  constructor(cwd: string) {
    this.cwd = cwd;
  }

  /**
   * Initializes or verifies a shadow Git repository for checkpoint tracking.
   * Creates a new repository if one doesn't exist, or verifies the worktree
   * configuration if it does.
   *
   * Key operations:
   * - Creates/verifies shadow git repository
   * - Configures git settings (user, LFS, etc.)
   * - Sets up worktree to point to workspace
   *
   * @param gitPath - Path to the .git directory
   * @param cwd - The current working directory for git operations
   * @returns Promise<string> Path to the initialized .git directory
   * @throws Error if:
   * - Worktree verification fails for existing repository
   * - Git initialization or configuration fails
   * - Unable to create initial commit
   * - LFS pattern setup fails
   */
  public async initShadowGit(gitPath: string, cwd: string): Promise<string> {
    console.info(`Initializing shadow git`);

    // If repo exists, just verify worktree
    if (await fileExistsAtPath(gitPath)) {
      const git = simpleGit(path.dirname(gitPath));
      const worktree = await git.getConfig('core.worktree');
      if (worktree.value !== cwd) {
        throw new Error('Checkpoints can only be used in the original workspace: ' + worktree.value);
      }
      console.warn(`Using existing shadow git at ${gitPath}`);

      // shadow git repo already exists, but update the excludes just in case
      await writeExcludesFile(gitPath, await getLfsPatterns(this.cwd));

      return gitPath;
    }

    // Initialize new repo
    const checkpointsDir = path.dirname(gitPath);
    console.warn(`Creating new shadow git in ${checkpointsDir}`);

    const git = simpleGit(checkpointsDir);
    await git.init();

    // Configure repo with git settings
    await git.addConfig('core.worktree', cwd);
    await git.addConfig('commit.gpgSign', 'false');
    await git.addConfig('user.name', 'JoyCode Checkpoint');
    await git.addConfig('user.email', '<EMAIL>');

    // Set up LFS patterns
    const lfsPatterns = await getLfsPatterns(cwd);
    await writeExcludesFile(gitPath, lfsPatterns);

    await this.addCheckpointFiles(git);

    // Initial commit only on first repo creation
    await git.commit('initial commit', { '--allow-empty': null });

    console.warn(`Shadow git initialization completed`);

    return gitPath;
  }

  /**
   * Retrieves the worktree path from the shadow git configuration.
   * The worktree path indicates where the shadow git repository is tracking files,
   * which should match the current workspace directory.
   *
   * @param gitPath - Path to the .git directory
   * @returns Promise<string | undefined> The worktree path or undefined if not found
   * @throws Error if unable to get worktree path
   */
  public async getShadowGitConfigWorkTree(gitPath: string): Promise<string | undefined> {
    try {
      const git = simpleGit(path.dirname(gitPath));
      const worktree = await git.getConfig('core.worktree');
      return worktree.value || undefined;
    } catch (error) {
      console.error('Failed to get shadow git config worktree:', error);
      return undefined;
    }
  }

  /**
   * Since we use git to track checkpoints, we need to temporarily disable nested git repos to work around git's
   * requirement of using submodules for nested repos.
   *
   * This method renames nested .git directories by adding/removing a suffix to temporarily disable/enable them.
   * The root .git directory is preserved. Uses VS Code's workspace API to find nested .git directories and
   * only processes actual directories (not files named .git).
   *
   * @param disable - If true, adds suffix to disable nested git repos. If false, removes suffix to re-enable them.
   * @throws Error if renaming any .git directory fails
   */
  public async renameNestedGitRepos(disable: boolean) {
    // Find all .git directories that are not at the root level
    const gitPaths = await globby('**/.git' + (disable ? '' : GIT_DISABLED_SUFFIX), {
      cwd: this.cwd,
      onlyDirectories: true,
      ignore: [
        '.git',
        'node_modules',
        '**/node_modules/**',
        'dist',
        'build',
        '*.log',
        '.DS_Store',
        '.vscode',
        ...DEFAULT_IGNORE_DIRS,
      ], // Ignore root level .git
      dot: true,
      markDirectories: false,
    });

    // For each nested .git directory, rename it based on operation
    for (const gitPath of gitPaths) {
      const fullPath = path.join(this.cwd, gitPath);
      let newPath: string;
      if (disable) {
        newPath = fullPath + GIT_DISABLED_SUFFIX;
      } else {
        newPath = fullPath.endsWith(GIT_DISABLED_SUFFIX) ? fullPath.slice(0, -GIT_DISABLED_SUFFIX.length) : fullPath;
      }

      try {
        await fs.rename(fullPath, newPath);
        console.log(`CheckpointTracker ${disable ? 'disabled' : 'enabled'} nested git repo ${gitPath}`);
      } catch (error) {
        console.error(
          `CheckpointTracker failed to ${disable ? 'disable' : 'enable'} nested git repo ${gitPath}:`,
          error
        );
      }
    }
  }

  /**
   * Adds files to the shadow git repository while handling nested git repos.
   * Uses git commands to list files and stages them for commit.
   * Respects .gitignore and handles LFS patterns.
   *
   * Process:
   * 1. Updates exclude patterns from LFS config
   * 2. Temporarily disables nested git repos
   * 3. Gets list of tracked and untracked files from git (respecting .gitignore)
   * 4. Adds all files to git staging
   * 5. Re-enables nested git repos
   *
   * @param git - SimpleGit instance configured for the shadow git repo
   * @returns Promise<CheckpointAddResult> Object containing success status, message, and file count
   * @throws Error if:
   *  - File operations fail
   *  - Git commands error
   *  - LFS pattern updates fail
   *  - Nested git repo handling fails
   */
  public async addCheckpointFiles(git: SimpleGit): Promise<CheckpointAddResult> {
    try {
      // Update exclude patterns before each commit
      await this.renameNestedGitRepos(true);
      console.info('Starting checkpoint add operation...');

      try {
        await git.add('.');
        return { success: true };
      } catch (error) {
        // 检测是否是CRLF/LF转换错误
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (
          errorMessage.includes('CRLF') ||
          errorMessage.includes('LF') ||
          errorMessage.includes('line endings') ||
          errorMessage.includes('line-endings')
        ) {
          console.warn('检测到CRLF/LF行尾转换错误，尝试修复...');

          // 尝试设置Git配置来解决行尾问题
          await git.addConfig('core.autocrlf', 'false');
          await git.addConfig('core.eol', 'lf');

          // 使用--renormalize选项重新添加文件，这会根据.gitattributes重新规范化行尾
          try {
            await git.add(['--renormalize', '.']);
            console.info('已成功修复CRLF/LF行尾转换问题');
            return { success: true };
          } catch (renormalizeError) {
            console.error('尝试修复CRLF/LF问题失败:', renormalizeError);
            throw renormalizeError;
          }
        }

        console.error('Checkpoint add operation failed:', error);
        throw error;
      }
    } catch (error) {
      console.error('Failed to add files to checkpoint', error);
      throw error;
    } finally {
      await this.renameNestedGitRepos(false);
    }
  }
}

export const GIT_DISABLED_SUFFIX = '_disabled';
