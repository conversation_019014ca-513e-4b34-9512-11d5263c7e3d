import * as vscode from 'vscode';

/**
 * Gets the contents of the active terminal
 * @returns The terminal contents as a string
 */
export async function getLatestTerminalOutput(): Promise<string> {
  // Store original clipboard content to restore later
  const originalClipboard = await vscode.env.clipboard.readText();

  try {
    // Select terminal content
    await vscode.commands.executeCommand('workbench.action.terminal.selectAll');

    // Copy selection to clipboard
    await vscode.commands.executeCommand('workbench.action.terminal.copySelection');

    // Clear the selection
    await vscode.commands.executeCommand('workbench.action.terminal.clearSelection');

    // Get terminal contents from clipboard
    let terminalContents = (await vscode.env.clipboard.readText()).trim();

    // Check if there's actually a terminal open
    if (terminalContents === originalClipboard) {
      return '';
    }

    // Clean up command separation
    const lines = terminalContents.split('\n');
    const lastLine = lines.pop()?.trim();
    if (lastLine) {
      let i = lines.length - 1;
      while (i >= 0 && !lines[i].trim().startsWith(lastLine)) {
        i--;
      }
      terminalContents = lines.slice(Math.max(i, 0)).join('\n');
    }

    return terminalContents;
  } finally {
    // Restore original clipboard content
    await vscode.env.clipboard.writeText(originalClipboard);
  }
}
