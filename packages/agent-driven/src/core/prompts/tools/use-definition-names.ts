import { ToolArgs } from './types';

export function getUseDefinitionNamesDescription(args: ToolArgs): string {
  return `
## use_definition_names

**Description:** This tool extracts and lists definition names (classes, functions, methods, variables, interfaces, types, etc.) from source code files. It can analyze either a single file or all files at the top level of a specified directory. The tool provides structural insights into codebases by identifying key constructs and their relationships, which is essential for understanding overall architecture and code organization.

**Parameters:**
- \`path\` (required): The directory path to analyze, specified relative to the current working directory \`${args.cwd}\`. When a directory is provided, the tool will process all top-level source files within that directory.

**Usage:**
<use_definition_names>
<path>target_path_here</path>
</use_definition_names>

**Examples:**

1. **Analyze all files in a directory:**
<use_definition_names>
<path>src/</path>
</use_definition_names>

2. **Analyze files in the current directory:**
<use_definition_names>
<path>.</path>
</use_definition_names>

**Expected Output:**
The tool returns a structured list of definitions found in the specified file(s), including:
- Function names and signatures
- Class names and their methods
- Variable and constant declarations
- Interface and type definitions
- Module exports and imports
- Other language-specific constructs

**Use Cases:**
- Code exploration and familiarization
- Architecture analysis and documentation
- Refactoring preparation
- Dependency mapping
- Code review assistance
`.trim();
}
