import { ToolArgs } from './types';

export function getUseWebSearchDescription(args: ToolArgs): string {
  return `## use_web_search
Description: Search the web for real-time information on a specific query. Use this tool when you need current, factual, or specialized information that may not be in your training data, such as recent news, current events, latest statistics, product information, or specific facts that require verification. This tool is essential for providing accurate, up-to-date responses.

Parameters:
- query: (required) The search query string. Should be specific, concise, and focused on the key information needed. Use relevant keywords and avoid overly broad terms. For best results, phrase queries as you would in a search engine (e.g., "latest iPhone 15 release date" rather than "tell me about new phones").

Usage:
<use_web_search>
<query>Your search query here</query>
</use_web_search>

Best Practices:
- Use specific, targeted keywords
- Include relevant context (dates, locations, brands) when applicable
- Avoid questions - use declarative search terms
- For current events, include time-relevant terms like "${new Date().getFullYear()}", "latest", "recent"`;
}
