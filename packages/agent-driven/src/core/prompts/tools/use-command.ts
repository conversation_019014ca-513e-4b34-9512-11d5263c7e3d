import { ToolArgs } from './types';

export function getUseCommandDescription(args: ToolArgs): string {
  return `## use_command
Description: Execute CLI commands on the system to perform operations and accomplish user tasks. Commands run in the current working directory: ${args.cwd}

Key Guidelines:
- <PERSON><PERSON> commands to the user's operating system
- Use proper shell chaining syntax when needed
- Prefer complex CLI commands over creating scripts for flexibility
- Always explain what the command does

Parameters:
- command: (required) The CLI command to execute. Must be valid for the current OS and properly formatted.
- requires_approval: (required) Boolean flag for user approval requirement.
  - Set to 'true' for: installing/uninstalling packages, deleting/modifying files, system configuration changes, network operations, or any potentially impactful operations
  - Set to 'false' for: reading files/directories, running dev servers, building projects, or other safe/non-destructive operations

Usage:
<use_command>
<command>Your command here</command>
<requires_approval>true or false</requires_approval>
</use_command>`;
}
