import { ToolArgs } from './types';

export function getNewTaskDescription(_args: ToolArgs): string {
  return `## new_task_creation
Description: Creates a new independent task instance with a specified operational mode and initial instructions. This tool spawns a new JoyCode agent instance that will operate in the designated mode and begin processing with the provided message as its starting context.

Parameters:
- mode: (required) The agentId identifier specifying which operational mode the new task should use (valid values include "code", "architect", or other available mode identifiers).
- message: (required) The initial user message, instructions, or context that will be provided to the new task instance as its starting point.

Usage:
<new_task_creation>
<mode>your-mode-agentId-here</mode>
<message>Your initial instructions here</message>
</new_task_creation>

Example:
<new_task_creation>
<mode>code</mode>
<message>Implement a new feature for the application.</message>
</new_task_creation>
`;
}
