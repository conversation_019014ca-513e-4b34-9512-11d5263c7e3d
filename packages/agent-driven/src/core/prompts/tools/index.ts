import { McpHub } from '../../../services/mcp/McpHub';
import { BrowserSettings } from '../../../shared/BrowserSettings';
import { ToolArgs } from './types';
import { getUseCommandDescription } from './use-command';
import { getUseReadFileDescription } from './use-read-file';
import { getUseWriteFileDescription } from './use-write-file';
import { getUseReplaceFileDescription } from './use-replace-file';
import { getUseSearchFilesDescription } from './use-search-files';
import { getUseListFilesDescription } from './use-list-files';
import { getUseDefinitionNamesDescription } from './use-definition-names';
import { getUseMcpToolsDescription } from './use-mcp-tools';
import { getGetMcpResourceDescription } from './get-mcp-resource';
import { getUseWebSearchDescription } from './use-web-search';
import { getGetUserQuestionDescription } from './get-user-question';
import { getAttemptTaskDoneDescription } from './attempt-task-done';
import { getGetPlanInfoDescription } from './ask-followup-question';
import { getUseCodebaseDescription } from './use-codebase'; //二进制仓库问答
import { getFetchInstructionsDescription } from './fetch-instructions';
import { getInsertContentDescription } from './insert-content';
import { getSwitchModeDescription } from './switch-mode';
import { getNewTaskDescription } from './new-task';
import { getGroupName, isToolAllowedForMode, Mode, ModeConfig } from '../../../shared/modes';
import { DiffStrategy } from '../../../shared/tools';
import { ToolName } from '../../../schemas';
import { experimentDefault } from '../../../shared/experiments';
import { getModeConfig, GroupEntry, TOOL_GROUPS } from '../../../../web-agent/src/utils/modes';
import { getUseBrowserTool } from '../getUseBrowserTool';
import { getNewTaskWithContextTool } from './new_task_with_condense_context';
import { getClearPublishDescription } from './clear-publish';
import { getErp, getJdhIdeUserInfo, getJdhLoginInfo } from '@joycoder/shared/src/loginJdh';

// 工具名称到描述函数的映射
const toolDescriptionMap: Record<string, (args: ToolArgs) => string | undefined> = {
  use_command: (args) => getUseCommandDescription(args),
  fetch_instructions: () => getFetchInstructionsDescription(),
  use_read_file: (args) => getUseReadFileDescription(args),
  use_write_file: (args) => getUseWriteFileDescription(args),
  use_replace_file: (args) => getUseReplaceFileDescription(args, true),
  use_search_files: (args) => getUseSearchFilesDescription(args),
  use_list_files: (args) => getUseListFilesDescription(args),
  use_definition_names: (args) => getUseDefinitionNamesDescription(args),
  use_mcp_tools: (args) => (args.mcpHub ? getUseMcpToolsDescription(args) : undefined),
  get_mcp_resource: (args) => (args.mcpHub ? getGetMcpResourceDescription(args) : undefined),
  use_web_search: (args) => getUseWebSearchDescription(args),
  get_user_question: (args) => getGetUserQuestionDescription(args),
  attempt_task_done: (args) => getAttemptTaskDoneDescription(args),
  ask_followup_question: (args) => getGetPlanInfoDescription(args),
  use_codebase: (args) => (args.supportsCodebase ? getUseCodebaseDescription(args) : undefined),
  insert_content: (args) => getInsertContentDescription(args),
  switch_mode: () => getSwitchModeDescription(),
  new_task_creation: (args) => getNewTaskDescription(args),
  new_task_with_condense_context: (args) => getNewTaskWithContextTool(args),
  use_browser: (args) => getUseBrowserTool(args),
  use_clear_publish: (args) => getClearPublishDescription(args),
  apply_diff: (args) =>
    args.diffStrategy ? args.diffStrategy.getToolDescription({ cwd: args.cwd, toolOptions: args.toolOptions }) : '',
};

// 始终可用的工具
const ALWAYS_AVAILABLE_TOOLS = [
  'get_user_question',
  'attempt_task_done',
  'switch_mode',
  'new_task_creation',
  'new_task_with_condense_context',
];

// 根据条件获取工具描述
export async function getToolDescriptions(
  cwd: string,
  mode: Mode,
  supportsComputerUse: boolean,
  supportsCodebase?: boolean,
  browserSettings?: BrowserSettings,
  mcpHub?: McpHub,
  customModes?: ModeConfig[],
  diffStrategy?: DiffStrategy
): Promise<string> {
  const config = getModeConfig(mode, customModes);
  const args: ToolArgs = {
    cwd,
    supportsComputerUse,
    diffStrategy,
    supportsCodebase,
    browserSettings,
    mcpHub,
  };

  // 确定要包含的工具
  const tools = new Set<string>(ALWAYS_AVAILABLE_TOOLS);
  // Add tools from mode's groups
  config.groups.forEach((groupEntry: GroupEntry) => {
    const groupName = getGroupName(groupEntry);
    const toolGroup = TOOL_GROUPS[groupName];
    if (toolGroup) {
      toolGroup.tools.forEach((tool) => {
        if (
          isToolAllowedForMode(tool as ToolName, mode, customModes ?? [], undefined, undefined, experimentDefault ?? {})
        ) {
          tools.add(tool);
        }
      });
    }
  });

  // 根据条件添加其他工具
  if (mcpHub) {
    tools.add('use_mcp_tools');
    tools.add('get_mcp_resource');
  }

  tools.add('use_web_search');

  // options工具
  tools.add('ask_followup_question');

  // 添加 use_codebase 工具
  if (supportsCodebase) {
    tools.add('use_codebase');
  }

  // 如果条件不满足，移除 use_browser 工具
  if (!(supportsComputerUse && browserSettings)) {
    tools.delete('use_browser');
  }

  const userInfo = await getJdhIdeUserInfo();
  const loginInfo = getJdhLoginInfo();
  const erp = await getErp(loginInfo?.userToken ?? '');
  if (erp || getJdhLoginInfo()?.erp || userInfo?.erp) {
    tools.add('use_clear_publish');
  }
  // 获取工具描述
  const descriptions = Array.from(tools).map((toolName) => {
    const descriptionFn = toolDescriptionMap[toolName];
    if (!descriptionFn) {
      return undefined;
    }
    return descriptionFn({
      ...args,
      toolOptions: undefined, // No tool options in group-based approach
    });
  });

  return `# Tools\n\n${descriptions.filter(Boolean).join('\n\n')}`;
}

// 导出所有工具描述函数，以便单独使用
export {
  getUseCommandDescription,
  getUseReadFileDescription,
  getUseWriteFileDescription,
  getUseReplaceFileDescription,
  getUseSearchFilesDescription,
  getUseListFilesDescription,
  getUseDefinitionNamesDescription,
  getUseMcpToolsDescription,
  getGetMcpResourceDescription,
  getUseWebSearchDescription,
  getGetUserQuestionDescription,
  getAttemptTaskDoneDescription,
  getGetPlanInfoDescription,
  getUseCodebaseDescription,
  getFetchInstructionsDescription,
  getInsertContentDescription,
  getSwitchModeDescription,
  ALWAYS_AVAILABLE_TOOLS,
};
