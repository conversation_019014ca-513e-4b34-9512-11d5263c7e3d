import * as path from 'path';
import * as vscode from 'vscode';

import { GlobalFileNames } from '../../storage/disk';

export async function createModeInstructions(context: vscode.ExtensionContext | undefined): Promise<string> {
  if (!context) throw new Error('Missing VSCode Extension Context');

  const settingsDir = path.join(context.globalStorageUri.fsPath, 'settings');
  const customModesPath = path.join(settingsDir, GlobalFileNames.customModes);

  return `
Custom modes can be configured in two ways:
  1. Globally via '${customModesPath}' (created automatically on startup)
  2. Per-workspace via 'mode.json' in the workspace root directory

When modes with the same agentId exist in both files, the workspace-specific mode.json version takes precedence. This allows projects to override global modes or define project-specific modes.


If asked to create a project mode, create it in mode.json in the workspace root. If asked to create a global mode, use the global custom modes file.

- The following fields are required and must not be empty:
  * agentId: A valid agentId (lowercase letters, numbers, and hyphens). Must be unique, and shorter is better.
  * name: The display name for the mode
  * agentDefinition: A detailed description of the mode's role and capabilities
  * groups: Array of allowed tool groups (can be empty). Each group can be specified either as a string (e.g., "edit" to allow editing any file) or with file restrictions (e.g., ["edit", { fileRegex: "\\.md$", description: "Markdown files only" }] to only allow editing markdown files)

- The following fields are optional but highly recommended:
  * whenToUse: A clear description of when this mode should be selected and what types of tasks it's best suited for. This helps the Orchestrator mode make better decisions.
  * customInstructions: Additional instructions for how the mode should operate

- For multi-line text, include newline characters in the string like "This is the first line.\\nThis is the next line.\\n\\nThis is a double line break."

Both files should follow this structure:
{
 "customModes": [
   {
     "agentId": "designer", // Required: unique agentId with lowercase letters, numbers, and hyphens
     "name": "Designer", // Required: mode display name
     "agentDefinition": "You are Roo, a UI/UX expert specializing in design systems and frontend development. Your expertise includes:\\n- Creating and maintaining design systems\\n- Implementing responsive and accessible web interfaces\\n- Working with CSS, HTML, and modern frontend frameworks\\n- Ensuring consistent user experiences across platforms", // Required: non-empty
     "whenToUse": "Use this mode when creating or modifying UI components, implementing design systems, or ensuring responsive web interfaces. This mode is especially effective with CSS, HTML, and modern frontend frameworks.", // Optional but recommended
     "groups": [ // Required: array of tool groups (can be empty)
       "read",    // Read files group (use_read_file, fetch_instructions, use_search_files, use_list_files, use_definition_names)
       "edit",    // Edit files group (apply_diff, use_write_file) - allows editing any file
       // Or with file restrictions:
       // ["edit", { fileRegex: "\\.md$", description: "Markdown files only" }],  // Edit group that only allows editing markdown files
       "browser", // Browser group (use_browser)
       "command", // Command group (use_command)
       "mcp"     // MCP group (use_mcp_tools, get_mcp_resource)
     ],
     "customInstructions": "Additional instructions for the Designer mode" // Optional
    }
  ]
}`;
}
