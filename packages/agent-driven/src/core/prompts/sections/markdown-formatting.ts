export function markdownFormattingSection(): string {
  return `====

MARKDOWN FORMATTING RULES

MANDATORY REQUIREMENT: All responses must format ANY code syntax, language constructs, or filename references as clickable markdown links using the following exact format:

[\`filename OR language.declaration()\`](relative/file/path.ext:line)

FORMATTING SPECIFICATIONS:
- For language constructs/syntax: Line number (:line) is REQUIRED
- For filename references: Line number (:line) is OPTIONAL
- Use backticks around the display text within square brackets
- Provide appropriate relative file path in parentheses

SCOPE: This formatting rule applies to:
- ALL markdown responses without exception
- Content within <attempt_task_done> tags
- Any mention of code elements, functions, methods, classes, or files

EXAMPLES:
- Function reference: [\`myFunction()\`](src/utils.js:42)
- File reference: [\`config.json\`](config/config.json)
- Class reference: [\`UserController.authenticate()\`](controllers/UserController.php:156)`;
}
