import { getShell } from '../../../utils/shell';
import os from 'os';
import osName from 'os-name';
import '../../../utils/path'; // Import to ensure String.prototype.toPosix is available
import { isIDE } from '@joycoder/shared';
import { isRemoteEnvironment } from '../../../utils/fs';

const isRemoteEnv = isRemoteEnvironment();
const isIDEnv = isIDE();

export function getSystemInfoSection(cwd: string): string {
  return `====

SYSTEM INFORMATION

Operating System: ${isIDEnv && isRemoteEnv ? 'Linux' : osName()}
Default Shell: ${isIDEnv && isRemoteEnv ? '/bin/bash' : getShell()}
Home Directory: ${isIDEnv && isRemoteEnv ? '/config' : os.homedir().toPosix()}
Current Working Directory: ${isIDEnv && isRemoteEnv ? '/config/workspace' : cwd.toPosix()}`;
}
