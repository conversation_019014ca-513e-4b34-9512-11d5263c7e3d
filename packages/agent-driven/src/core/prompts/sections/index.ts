import fs from 'fs/promises';
import path from 'path';
import { Mode } from '../../../shared/modes';
import {
  directoryExists,
  formatDirectoryContent,
  loadRuleFiles,
  readTextFilesFromDirectory,
} from './custom-instructions';
export { getCapabilitiesSection } from './capabilities';
export { getMcpServersSection } from './mcp-servers';
export { getModesSection } from './modes';
export { getObjectiveSection } from './objective';
export { getRulesSection } from './rules';
export { getSystemInfoSection } from './system-info';
export { getSharedToolUseSection } from './tool-use';
export { getToolUseGuidelinesSection } from './tool-use-guidelines';
export { getToolUseExamples } from './tool-use-examples';
export { markdownFormattingSection } from './markdown-formatting';
export { getMemoryModuleInstructions } from './memory-module';

export type PromptVariables = {
  workspace?: string;
  mode?: string;
  language?: string;
  shell?: string;
  operatingSystem?: string;
};

function interpolatePromptContent(content: string, variables: PromptVariables): string {
  let interpolatedContent = content;
  for (const key in variables) {
    if (Object.prototype.hasOwnProperty.call(variables, key) && variables[key as keyof PromptVariables] !== undefined) {
      const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      interpolatedContent = interpolatedContent.replace(placeholder, variables[key as keyof PromptVariables]!);
    }
  }
  return interpolatedContent;
}

/**
 * Safely reads a file, returning an empty string if the file doesn't exist
 */
async function safeReadFile(filePath: string): Promise<string> {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    // When reading with "utf-8" encoding, content should be a string
    return content.trim();
  } catch (err) {
    const errorCode = (err as NodeJS.ErrnoException).code;
    if (!errorCode || !['ENOENT', 'EISDIR'].includes(errorCode)) {
      throw err;
    }
    return '';
  }
}

/**
 * Get the path to a system prompt file for a specific mode
 */
export function getSystemPromptFilePath(cwd: string, mode: Mode): string {
  return path.join(cwd, '.joycode', 'agents', `agent-${mode}`);
}

/**
 * Loads custom system prompt from a file at .roo/system-prompt-[mode agentId]
 * If the file doesn't exist, returns an empty string
 */
export async function loadSystemPromptFile(cwd: string, mode: Mode, variables: PromptVariables): Promise<string> {
  const filePath = getSystemPromptFilePath(cwd, mode);
  const rawContent = await safeReadFile(filePath);
  if (!rawContent) {
    return '';
  }
  const interpolatedContent = interpolatePromptContent(rawContent, variables);
  return interpolatedContent;
}

// 自定义指令部分
export async function addCustomInstructions(
  modeCustomInstructions: string,
  globalCustomInstructions: string,
  cwd: string,
  mode: string,
  options?: {
    language?: string;
    JoyCoderIgnoreInstructions?: string;
  }
): Promise<string> {
  const customInstructions = [];

  // Load mode-specific rules if mode is provided
  let modeRuleContent = '';
  let usedRuleFile = '';

  if (mode) {
    const modeRulesDir = path.join(cwd, '.joycode', `rules-${mode}`);
    if (await directoryExists(modeRulesDir)) {
      const files = await readTextFilesFromDirectory(modeRulesDir);
      if (files.length > 0) {
        modeRuleContent = formatDirectoryContent(modeRulesDir, files);
        usedRuleFile = modeRulesDir;
      }
    }

    // If no directory exists, fall back to existing behavior
    if (!modeRuleContent) {
      const rooModeRuleFile = `.joycoderules-${mode}`;
      modeRuleContent = await safeReadFile(path.join(cwd, '.joycode', rooModeRuleFile));
      if (modeRuleContent) {
        usedRuleFile = rooModeRuleFile;
      }
    }
  }

  // 添加模式特定的自定义指令
  if (modeCustomInstructions) {
    customInstructions.push(`====\nMode-specific Instructions:\n\n***${modeCustomInstructions.trim()}***`);
  }

  // 添加全局自定义指令
  if (globalCustomInstructions) {
    customInstructions.push(`====\n\nGlobal Instructions:\n\n${globalCustomInstructions.trim()}`);
  }

  // Add rules - include both mode-specific and generic rules if they exist
  const rules = [];
  const sections = [];
  // Add mode-specific rules first if they exist
  if (modeRuleContent && modeRuleContent.trim()) {
    if (usedRuleFile.includes(path.join('.joycode', `rules-${mode}`))) {
      rules.push(modeRuleContent.trim());
    } else {
      rules.push(`# Rules from ${usedRuleFile}:\n${modeRuleContent}`);
    }
  }

  // Add generic rules
  const genericRuleContent = await loadRuleFiles(cwd);
  if (genericRuleContent && genericRuleContent.trim()) {
    rules.push(genericRuleContent.trim());
  }
  if (rules.length > 0) {
    sections.push(`Rules:\n\n${rules.join('\n\n')}`);
  }

  const joinedSections = sections.join('\n\n');
  customInstructions.push(joinedSections);

  // 添加 JoyCoderIgnore 指令
  if (options?.JoyCoderIgnoreInstructions) {
    customInstructions.push(`\n====\n\nJOYCODERIGNORE INSTRUCTIONS:\n\n${options.JoyCoderIgnoreInstructions}`);
  }

  return `
====

USER'S CUSTOM INSTRUCTIONS

The following additional instructions are provided by the user, and should be followed to the best of your ability without interfering with the TOOL USE guidelines.

${customInstructions.join('\n\n')}`;
}
