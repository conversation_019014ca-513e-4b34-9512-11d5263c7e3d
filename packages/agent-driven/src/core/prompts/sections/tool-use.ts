export function getSharedToolUseSection(): string {
  return `# TOOL USAGE INSTRUCTIONS

You have access to a set of tools that require user approval before execution. Follow these rules strictly:

## Core Rules
- Wait for the tool execution result before proceeding
- Use each tool result to inform your next action
- Work step-by-step toward completing the given task

## Tool Formatting Requirements

All tool calls must use XML-style formatting with the following structure:
<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
</tool_name>

## Important Formatting Notes:
- The tool name becomes the XML tag name (e.g., \`<new_task_creation>\`)
- Each parameter gets its own XML tag pair
- Parameter values go between the opening and closing tags
- Maintain proper XML structure for successful parsing
## Example Usage:
<new_task_creation>
<mode>code</mode>
<message>Implement a new feature for the application.</message>
</new_task_creation>

## Execution Flow
1. Analyze the task requirements
2. Select the appropriate tool
3. Format the tool call correctly using XML tags
4. Wait for execution results
5. Use results to determine next steps
6. Repeat until task completion

Always ensure your tool calls use the exact tool name as the XML tag for proper system recognition and execution.
`;
}
