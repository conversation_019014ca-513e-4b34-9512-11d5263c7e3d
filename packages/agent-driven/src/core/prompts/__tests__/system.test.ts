import { SYSTEM_PROMPT } from '../system';
import { createMockMcpHub, createMockBrowserSettings } from './utils';
import { BrowserSettings } from '../../../shared/BrowserSettings';

describe('SYSTEM_PROMPT', () => {
  // 创建一个模拟的 vscode.ExtensionContext
  const mockContext = {
    extensionPath: '/mock/extension/path',
    subscriptions: [],
    workspaceState: {
      get: jest.fn(),
      update: jest.fn(),
    },
    globalState: {
      get: jest.fn(),
      update: jest.fn(),
      setKeysForSync: jest.fn(),
    },
  } as any;
  const cwd = '/test/path';
  let mockMcpHub: any;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should generate basic system prompt', async () => {
    const prompt = await SYSTEM_PROMPT(
      mockContext,
      cwd,
      false, // supportsComputerUse
      undefined, // mcpHub
      false, // supportsCodebase
      undefined, // browserSettings
      true // enableMcpServerCreation
    );

    // 基本系统提示词应该包含
    expect(prompt).toContain('TOOL USE');
    expect(prompt).toContain('CAPABILITIES');
    expect(prompt).toContain('RULES');
    expect(prompt).toContain('OBJECTIVE');
  });

  it('should include browser actions when supportsComputerUse is true', async () => {
    const browserSettings: BrowserSettings = createMockBrowserSettings();
    const prompt = await SYSTEM_PROMPT(
      mockContext,
      cwd,
      true, // supportsComputerUse
      undefined, // mcpHub
      false, // supportsCodebase
      browserSettings, // browserSettings
      true // enableMcpServerCreation
    );

    // 应该包含浏览器相关工具
    expect(prompt).toContain('use_browser');
    expect(prompt).toContain('browser_goto');
    expect(prompt).toContain('browser_click');
    expect(prompt).toContain('browser_fill');
  });

  it('should include MCP server info when mcpHub is provided', async () => {
    mockMcpHub = createMockMcpHub();

    const prompt = await SYSTEM_PROMPT(
      mockContext,
      cwd,
      false, // supportsComputerUse
      mockMcpHub, // mcpHub
      false, // supportsCodebase
      undefined, // browserSettings
      true // enableMcpServerCreation
    );

    // 应该包含 MCP 相关信息
    expect(prompt).toContain('MCP SERVERS');
    expect(prompt).toContain('use_mcp_tools');
    expect(prompt).toContain('get_mcp_resource');
  });

  it('should include custom instructions when provided', async () => {
    const customInstructions = 'Custom test instructions';

    // 这个测试需要使用 addUserInstructions 函数
    const userInstructions = await import('../system').then((module) => module.addUserInstructions(customInstructions));

    const prompt = await SYSTEM_PROMPT(
      mockContext,
      cwd,
      false, // supportsComputerUse
      undefined, // mcpHub
      false, // supportsCodebase
      undefined, // browserSettings
      true // enableMcpServerCreation
    );

    // 应该包含自定义指令
    expect(prompt).toContain("USER'S CUSTOM INSTRUCTIONS");
    expect(prompt).toContain(customInstructions);
  });
});
