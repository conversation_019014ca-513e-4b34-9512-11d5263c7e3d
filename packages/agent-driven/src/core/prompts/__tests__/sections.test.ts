import { getCapabilitiesSection } from '../sections/capabilities';
import { getModesSection } from '../sections/modes';
import { getSystemInfoSection } from '../sections/system-info';
import { createMockMcpHub } from './utils';

describe('Prompt Sections', () => {
  const cwd = '/test/path';

  describe('getCapabilitiesSection', () => {
    it('should include basic capabilities', () => {
      const result = getCapabilitiesSection(cwd, false);

      // 基本能力描述应该包含
      expect(result).toContain('CAPABILITIES');
      expect(result).toContain('tools that let you execute CLI commands');
      expect(result).toContain('list files');
      expect(result).toContain('view source code');
    });

    it('should include browser capabilities when supportsComputerUse is true', () => {
      const result = getCapabilitiesSection(cwd, true);

      // 应该包含浏览器相关能力
      expect(result).toContain('browser');
      expect(result).toContain('interact with web content');
    });

    it('should include MCP capabilities when mcpHub is provided', () => {
      const mockMcpHub = createMockMcpHub();
      const result = getCapabilitiesSection(cwd, false, mockMcpHub as any);

      // 应该包含 MCP 相关能力
      expect(result).toContain('MCP servers');
    });
  });

  describe('getModesSection', () => {
    it('should include mode descriptions', async () => {
      // 创建一个模拟的 vscode.ExtensionContext
      const mockContext = new (require('vscode').ExtensionContext)();
      const result = await getModesSection(mockContext);

      // 应该包含模式描述
      expect(result).toContain('MODES');
      expect(result).toContain('ACT MODE');
      expect(result).toContain('PLAN MODE');
    });
  });

  describe('getSystemInfoSection', () => {
    it('should include system information', () => {
      const result = getSystemInfoSection(cwd);

      // 应该包含系统信息
      expect(result).toContain('SYSTEM INFORMATION');
      expect(result).toContain('Current Working Directory');
      expect(result).toContain(cwd);
    });
  });
});
