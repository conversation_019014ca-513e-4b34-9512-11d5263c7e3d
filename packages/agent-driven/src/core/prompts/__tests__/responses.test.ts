import { formatResponse } from '../responses';
import { JoyCoderIgnoreController, LOCK_TEXT_SYMBOL } from '../../ignore/JoycoderIgnoreController';
import { fileExistsAtPath } from '../../../utils/fs';
import * as fs from 'fs/promises';
import { toPosix } from './utils';

// 模拟依赖
jest.mock('../../../utils/fs');
jest.mock('fs/promises');
jest.mock('vscode', () => {
  const mockDisposable = { dispose: jest.fn() };
  return {
    workspace: {
      createFileSystemWatcher: jest.fn(() => ({
        onDidCreate: jest.fn(() => mockDisposable),
        onDidChange: jest.fn(() => mockDisposable),
        onDidDelete: jest.fn(() => mockDisposable),
        dispose: jest.fn(),
      })),
    },
    RelativePattern: jest.fn(),
  };
});

describe('响应格式化测试', () => {
  const TEST_CWD = '/test/path';
  let mockFileExists: jest.MockedFunction<typeof fileExistsAtPath>;
  let mockReadFile: jest.MockedFunction<typeof fs.readFile>;

  beforeEach(() => {
    // 重置模拟
    jest.clearAllMocks();

    // 设置 fs 模拟
    mockFileExists = fileExistsAtPath as jest.MockedFunction<typeof fileExistsAtPath>;
    mockReadFile = fs.readFile as jest.MockedFunction<typeof fs.readFile>;

    // 默认模拟实现
    mockFileExists.mockResolvedValue(true);
    mockReadFile.mockResolvedValue('node_modules\n.git\nsecrets/**\n*.log');
  });

  describe('formatResponse.joycoderIgnoreError', () => {
    /**
     * 测试被忽略文件的错误消息格式
     */
    it('应该格式化被忽略文件的错误消息', () => {
      const errorMessage = formatResponse.joycoderIgnoreError('secrets/api-keys.json');

      // 验证错误消息格式
      expect(errorMessage).toContain('Access to secrets/api-keys.json is blocked by the .joycoderignore file settings');
      expect(errorMessage).toContain('continue in the task without using this file');
      expect(errorMessage).toContain('ask the user to update the .joycoderignore file');
    });

    /**
     * 使用不同的文件路径进行测试
     */
    it('应该在错误消息中包含文件路径', () => {
      const paths = ['node_modules/package.json', '.git/HEAD', 'secrets/credentials.env', 'logs/app.log'];

      // 测试每个路径
      for (const testPath of paths) {
        const errorMessage = formatResponse.joycoderIgnoreError(testPath);
        expect(errorMessage).toContain(`Access to ${testPath} is blocked`);
      }
    });
  });

  describe('formatResponse.formatFilesList 与 JoyCoderIgnoreController', () => {
    /**
     * 测试带有 joycoderignore 控制器的文件列表格式化
     */
    it('应该为被忽略的文件添加锁定符号', async () => {
      // 创建控制器
      const controller = new JoyCoderIgnoreController(TEST_CWD);
      await controller.initialize();

      // 模拟 validateAccess 以控制哪些文件被忽略
      controller.validateAccess = jest.fn().mockImplementation((filePath: string) => {
        // 只允许不匹配这些模式的文件
        return (
          !filePath.includes('node_modules') && !filePath.includes('.git') && !toPosix(filePath).includes('secrets/')
        );
      });

      // 混合允许/忽略文件的文件列表
      const files = [
        'src/app.ts', // 允许
        'node_modules/package.json', // 忽略
        'README.md', // 允许
        '.git/HEAD', // 忽略
        'secrets/keys.json', // 忽略
      ];

      // 使用控制器格式化
      const result = formatResponse.formatFilesList(TEST_CWD, files, false, controller as any);

      // 应该包含每个文件
      expect(result).toContain('src/app.ts');
      expect(result).toContain('README.md');

      // 应该为被忽略的文件添加锁定符号 - 使用正则表达式进行不区分大小写的检查
      expect(result).toMatch(new RegExp(`${LOCK_TEXT_SYMBOL}.*node_modules/package.json`, 'i'));
      expect(result).toMatch(new RegExp(`${LOCK_TEXT_SYMBOL}.*\\.git/HEAD`, 'i'));
      expect(result).toMatch(new RegExp(`${LOCK_TEXT_SYMBOL}.*secrets/keys.json`, 'i'));

      // 允许的文件不应该有锁定符号
      expect(result).not.toContain(`${LOCK_TEXT_SYMBOL} src/app.ts`);
      expect(result).not.toContain(`${LOCK_TEXT_SYMBOL} README.md`);
    });

    /**
     * 测试 formatFilesList 处理截断
     */
    it('应该正确处理带有 JoyCoderIgnoreController 的截断', async () => {
      // 创建控制器
      const controller = new JoyCoderIgnoreController(TEST_CWD);
      await controller.initialize();

      // 使用控制器和截断标志格式化
      const result = formatResponse.formatFilesList(
        TEST_CWD,
        ['file1.txt', 'file2.txt'],
        true, // didHitLimit = true
        controller as any
      );

      // 应该包含截断消息（不区分大小写检查）
      expect(result).toContain('File list truncated');
      expect(result).toMatch(/use use_list_files on specific subdirectories/i);
    });

    /**
     * 测试 formatFilesList 处理空结果
     */
    it('应该处理带有 JoyCoderIgnoreController 的空文件列表', async () => {
      // 创建控制器
      const controller = new JoyCoderIgnoreController(TEST_CWD);
      await controller.initialize();

      // 使用空文件数组格式化
      const result = formatResponse.formatFilesList(TEST_CWD, [], false, controller as any);

      // 应该显示 "No files found"
      expect(result).toBe('No files found.');
    });
  });

  describe('formatResponse.toolResult', () => {
    it('应该返回纯文本结果', () => {
      const text = '这是一个测试结果';
      const result = formatResponse.toolResult(text);

      expect(result).toBe(text);
    });

    it('应该返回带有图像的结果', () => {
      const text = '这是一个带有图像的测试结果';
      const images = [
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==',
      ];
      const result = formatResponse.toolResult(text, images);

      expect(Array.isArray(result)).toBe(true);
      if (Array.isArray(result)) {
        const textBlock = result[0] as any;
        const imageBlock = result[1] as any;
        expect(textBlock.type).toBe('text');
        expect(textBlock.text).toBe(text);
        expect(imageBlock.type).toBe('image');
      }
    });
  });

  describe('formatResponse.createPrettyPatch', () => {
    it('应该创建漂亮的补丁', () => {
      const oldStr = 'console.log("Hello");\n';
      const newStr = 'console.log("Hello World");\n';
      const result = formatResponse.createPrettyPatch('test.js', oldStr, newStr);

      expect(result).toContain('-console.log("Hello");');
      expect(result).toContain('+console.log("Hello World");');
    });

    it('应该处理未定义的字符串', () => {
      const result = formatResponse.createPrettyPatch('test.js', undefined, 'console.log("Hello World");\n');

      expect(result).toContain('+console.log("Hello World");');
    });
  });

  describe('formatResponse.duplicateFileReadNotice', () => {
    it('应该返回重复文件读取通知', () => {
      const result = formatResponse.duplicateFileReadNotice();

      expect(result).toContain('This file read has been removed');
      expect(result).toContain('Refer to the latest file read');
    });
  });

  describe('formatResponse.contextTruncationNotice', () => {
    it('应该返回上下文截断通知', () => {
      const result = formatResponse.contextTruncationNotice();

      expect(result).toContain('Some previous conversation history');
      expect(result).toContain('removed to maintain optimal context window length');
    });
  });

  describe('formatResponse.condense', () => {
    it('应该返回浓缩响应', () => {
      const result = formatResponse.condense();

      expect(result).toContain('The user has accepted the condensed conversation summary');
      expect(result).toContain('ONLY asking the user what you should work on next');
    });
  });

  describe('formatResponse.toolDenied', () => {
    it('应该返回工具被拒绝的消息', () => {
      const result = formatResponse.toolDenied();

      expect(result).toBe('The user denied this operation.');
    });
  });

  describe('formatResponse.toolDeniedWithFeedback', () => {
    it('应该返回带有反馈的工具被拒绝的消息', () => {
      const feedback = '这个操作可能会导致数据丢失';
      const result = formatResponse.toolDeniedWithFeedback(feedback);

      expect(result).toContain('The user denied this operation');
      expect(result).toContain(feedback);
    });
  });

  describe('formatResponse.toolError', () => {
    it('应该返回工具错误消息', () => {
      const error = '无法访问文件：权限被拒绝';
      const result = formatResponse.toolError(error);

      expect(result).toContain('The tool execution failed');
      expect(result).toContain(error);
    });
  });

  describe('formatResponse.noToolsUsed', () => {
    it('应该返回未使用工具的错误消息', () => {
      const result = formatResponse.noToolsUsed();

      expect(result).toContain('You did not use a tool in your previous response');
      expect(result).toContain('Please retry with a tool use');
    });
  });

  describe('formatResponse.tooManyMistakes', () => {
    it('应该返回太多错误的消息', () => {
      const feedback = '请尝试使用不同的方法';
      const result = formatResponse.tooManyMistakes(feedback);

      expect(result).toContain('You seem to be having trouble proceeding');
      expect(result).toContain(feedback);
    });
  });

  describe('formatResponse.missingToolParameterError', () => {
    it('应该返回缺少工具参数的错误消息', () => {
      const paramName = 'path';
      const result = formatResponse.missingToolParameterError(paramName);

      expect(result).toContain(`Missing value for required parameter '${paramName}'`);
      expect(result).toContain('Please retry with complete response');
    });
  });

  describe('formatResponse.invalidMcpToolArgumentError', () => {
    it('应该返回无效的 MCP 工具参数错误消息', () => {
      const serverName = 'weather-server';
      const toolName = 'get_forecast';
      const result = formatResponse.invalidMcpToolArgumentError(serverName, toolName);

      expect(result).toContain(`Invalid JSON argument used with ${serverName} for ${toolName}`);
      expect(result).toContain('Please retry with a properly formatted JSON argument');
    });
  });
});
