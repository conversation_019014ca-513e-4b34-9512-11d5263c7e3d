import { ToolName } from '../../schemas';
import { isToolAllowedForMode, Mode, ModeConfig } from '../../shared/modes';

export function validateToolUse(
  toolName: ToolName,
  mode: Mode,
  customModes?: ModeConfig[],
  toolRequirements?: Record<string, boolean>,
  toolParams?: Record<string, any>
): void {
  if (!isToolAllowedForMode(toolName, mode, customModes ?? [], toolRequirements, toolParams)) {
    throw new Error(`Tool "${toolName}" is not allowed in ${mode} mode.`);
  }
}
