import path from 'path';
import { fileExistsAtPath } from '../../utils/fs';
import fs from 'fs/promises';
import ignore, { Ignore } from 'ignore';
import * as vscode from 'vscode';
import { DEFAULT_IGNORE_DIRS } from '@joycoder/shared';
import { FileSystemHelper } from '../../utils/FileSystemHelper';

export const LOCK_TEXT_SYMBOL = '\u{1F512}';

/**
 * Controls LLM access to files by enforcing ignore patterns.
 * Designed to be instantiated once in JoyCoder.ts and passed to file manipulation services.
 * Uses the 'ignore' library to support standard .gitignore syntax in .joycoderignore files.
 */
export class JoyCoderIgnoreController {
  private cwd: string | vscode.Uri;
  private ignoreInstance: Ignore;
  private disposables: vscode.Disposable[] = [];
  joycoderIgnoreContent: string | undefined;
  private defaultIgnorePatterns: string[] = [
    '.git',
    'node_modules',
    'dist',
    'build',
    '*.log',
    '.DS_Store',
    '.vscode',
    '*.tmp',
    '*.temp',
    '*.swp',
    '*.swo',
    'yarn-error.log',
    'npm-debug.log',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.env',
    '.env.*',
    '*.bak',
    '*.cache',
    ...DEFAULT_IGNORE_DIRS,
  ];

  constructor(cwd: string | vscode.Uri) {
    this.cwd = cwd;
    this.ignoreInstance = ignore();
    this.joycoderIgnoreContent = undefined;
    // Set up file watcher for .joycoderignore
    this.setupFileWatcher();
    // Add default ignore patterns
    this.ignoreInstance.add(this.defaultIgnorePatterns);
  }

  /**
   * Initialize the controller by loading custom patterns
   * Must be called after construction and before using the controller
   */
  async initialize(): Promise<void> {
    await this.loadJoyCoderIgnore();
  }

  /**
   * Set up the file watcher for .joycoderignore changes
   */
  private setupFileWatcher(): void {
    const joycoderignorePattern = new vscode.RelativePattern(this.cwd, '.joycodeignore');
    const fileWatcher = vscode.workspace.createFileSystemWatcher(joycoderignorePattern);

    // Watch for changes and updates
    this.disposables.push(
      fileWatcher.onDidChange(() => {
        this.loadJoyCoderIgnore();
      }),
      fileWatcher.onDidCreate(() => {
        this.loadJoyCoderIgnore();
      }),
      fileWatcher.onDidDelete(() => {
        this.loadJoyCoderIgnore();
      })
    );

    // Add fileWatcher itself to disposables
    this.disposables.push(fileWatcher);
  }

  /**
   * Load custom patterns from .joycoderignore if it exists
   */
  private async loadJoyCoderIgnore(): Promise<void> {
    try {
      // Reset ignore instance to prevent duplicate patterns
      this.ignoreInstance = ignore();
      // Add default ignore patterns
      this.ignoreInstance.add(this.defaultIgnorePatterns);
      const ignorePath = FileSystemHelper.join(this.cwd, '.joycoderignore');
      if (await fileExistsAtPath(ignorePath)) {
        const content = await fs.readFile(ignorePath, 'utf8');
        this.joycoderIgnoreContent = content;
        this.ignoreInstance.add(content);
        this.ignoreInstance.add('.joycoderignore');
      } else {
        this.joycoderIgnoreContent = undefined;
      }
    } catch (error) {
      // Should never happen: reading file failed even though it exists
      console.error('Unexpected error loading .joycoderignore:', error);
    }
  }

  /**
   * Check if a file should be accessible to the LLM
   * @param filePath - Path to check (relative to cwd)
   * @returns true if file is accessible, false if ignored
   */
  validateAccess(filePath: string): boolean {
    // Always allow access if .joycoderignore does not exist
    if (!this.joycoderIgnoreContent) {
      return true;
    }
    try {
      // Normalize path to be relative to cwd and use forward slashes
      const absolutePath = FileSystemHelper.resolve(this.cwd, filePath);
      const relativePath = FileSystemHelper.relative(this.cwd, absolutePath).toPosix();

      // Ignore expects paths to be path.relative()'d
      return !this.ignoreInstance.ignores(relativePath);
    } catch (error) {
      // console.error(`Error validating access for ${filePath}:`, error)
      // Ignore is designed to work with relative file paths, so will throw error for paths outside cwd. We are allowing access to all files outside cwd.
      return true;
    }
  }

  /**
   * Check if a terminal command should be allowed to execute based on file access patterns
   * @param command - Terminal command to validate
   * @returns path of file that is being accessed if it is being accessed, undefined if command is allowed
   */
  validateCommand(command: string): string | undefined {
    // Always allow if no .joycoderignore exists
    if (!this.joycoderIgnoreContent) {
      return undefined;
    }

    // Split command into parts and get the base command
    const parts = command.trim().split(/\s+/);
    const baseCommand = parts[0].toLowerCase();

    // Commands that read file contents
    const fileReadingCommands = [
      // Unix commands
      'cat',
      'less',
      'more',
      'head',
      'tail',
      'grep',
      'awk',
      'sed',
      // PowerShell commands and aliases
      'get-content',
      'gc',
      'type',
      'select-string',
      'sls',
    ];

    if (fileReadingCommands.includes(baseCommand)) {
      // Check each argument that could be a file path
      for (let i = 1; i < parts.length; i++) {
        const arg = parts[i];
        // Skip command flags/options (both Unix and PowerShell style)
        if (arg.startsWith('-') || arg.startsWith('/')) {
          continue;
        }
        // Ignore PowerShell parameter names
        if (arg.includes(':')) {
          continue;
        }
        // Validate file access
        if (!this.validateAccess(arg)) {
          return arg;
        }
      }
    }

    return undefined;
  }

  /**
   * Filter an array of paths, removing those that should be ignored
   * @param paths - Array of paths to filter (relative to cwd)
   * @returns Array of allowed paths
   */
  filterPaths(paths: string[]): string[] {
    try {
      return paths
        .map((p) => ({
          path: p,
          allowed: this.validateAccess(p),
        }))
        .filter((x) => x.allowed)
        .map((x) => x.path);
    } catch (error) {
      console.error('Error filtering paths:', error);
      return []; // Fail closed for security
    }
  }

  /**
   * Clean up resources when the controller is no longer needed
   */
  dispose(): void {
    this.disposables.forEach((d) => d.dispose());
    this.disposables = [];
  }
}
