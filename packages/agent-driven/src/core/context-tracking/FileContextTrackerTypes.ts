import * as vscode from 'vscode';

// Type definitions for FileContextTracker
export interface FileMetadataEntry {
  path: string;
  record_state: 'active' | 'stale';
  record_source: 'read_tool' | 'user_edited' | 'JoyCoder_edited' | 'file_mentioned';
  JoyCoder_read_date: number | null;
  JoyCoder_edit_date: number | null;
  user_edit_date?: number | null;
}

export interface TaskMetadata {
  files_in_context: FileMetadataEntry[];
}

// Interface for the controller to avoid direct dependency
export interface ControllerLike {
  context: vscode.ExtensionContext;
}
