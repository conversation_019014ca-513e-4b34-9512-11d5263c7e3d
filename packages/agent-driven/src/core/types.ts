import EventEmitter from 'events';

export type RooTerminalProvider = 'vscode' | 'execa';

export interface RooTerminalProcess extends EventEmitter<RooTerminalProcessEvents> {
  command: string;
  isHot: boolean;
  run: (command: string) => Promise<void>;
  continue: () => void;
  abort: () => void;
  hasUnretrievedOutput: () => boolean;
  getUnretrievedOutput: () => string;
}

export type RooTerminalProcessResultPromise = RooTerminalProcess & Promise<void>;

export interface RooTerminalProcessEvents {
  line: [line: string];
  continue: [];
  completed: [output?: string];
  stream_available: [stream: AsyncIterable<string>];
  shell_execution_started: [pid: number | undefined];
  shell_execution_complete: [exitDetails: ExitCodeDetails];
  error: [error: Error];
  no_shell_integration: [message: string];
}

export interface ExitCodeDetails {
  exitCode: number | undefined;
  signal?: number | undefined;
  signalName?: string;
  coreDumpPossible?: boolean;
}
