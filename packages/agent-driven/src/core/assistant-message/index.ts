export type AssistantMessageContent = TextContent | ToolUse;

export { parseAssistantMessage } from './parse-assistant-message';

export interface TextContent {
  type: 'text';
  content: string;
  partial: boolean;
  conversationId?: string;
  taskId?: string;
  sessionId?: string;
  userContent?: any;
  line_count?: number;
}

export const toolUseNames = [
  'use_command',
  'use_read_file',
  'use_web_search',
  'use_write_file',
  'use_replace_file',
  'use_search_files',
  'use_list_files',
  'use_definition_names',
  'use_browser',
  'use_mcp_tools',
  'get_mcp_resource',
  'get_user_question',
  'ask_followup_question',
  'attempt_task_done',
  'use_codebase',
  'codebase_search',
  'get_mcp_instructions',
  'new_task_creation',
  'condense',
  'insert_content',
  'fetch_instructions',
  'apply_diff',
  'new_task_with_condense_context',
  'switch_mode',
  'use_clear_publish',
] as const;

// Converts array of tool call names into a union type ("use_command" | "use_read_file" | ...)
export type ToolUseName = (typeof toolUseNames)[number];

export const toolParamNames = [
  'command',
  'requires_approval',
  'path',
  'content',
  'diff',
  'regex',
  'file_pattern',
  'recursive',
  'action',
  'url',
  'coordinate',
  'text',
  'server_name',
  'tool_name',
  'arguments',
  'uri',
  'question',
  'options',
  'response',
  'query',
  'result',
  'message',
  'start_line',
  'end_line',
  'reason',
  'operations',
  'line',
  'mode',
  'context',
  'mode_slug',
  'reason',
  'line_count',
  'task',
  'cwd',
  'follow_up',
  'search',
  'replace',
  'use_regex',
  'ignore_case',
  'size',
  'file_path',
  'duration',
  'project_path',
  'project_name',
  'args',
] as const;

export type ToolParamName = (typeof toolParamNames)[number];

export interface ToolUse {
  type: 'tool_use';
  name: ToolUseName;
  // params is a partial record, allowing only some or none of the possible parameters to be used
  params: Partial<Record<ToolParamName, string>>;
  partial: boolean;
  conversationId?: string;
  taskId?: string;
  sessionId?: string;
  userContent?: any;
}

export interface ExecuteCommandToolUse extends ToolUse {
  name: 'use_command';
  // Pick<Record<ToolParamName, string>, "command"> makes "command" required, but Partial<> makes it optional
  params: Partial<Pick<Record<ToolParamName, string>, 'command' | 'requires_approval'>>;
}

export interface ReadFileToolUse extends ToolUse {
  name: 'use_read_file';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'start_line' | 'end_line'>>;
}

export interface WriteToFileToolUse extends ToolUse {
  name: 'use_write_file';
  params: Required<Pick<Record<ToolParamName, string>, 'path' | 'content' | 'line_count'>>;
}

export interface InsertCodeBlockToolUse extends ToolUse {
  name: 'insert_content';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'line' | 'content'>>;
}

export interface ReplaceInFileToolUse extends ToolUse {
  name: 'use_replace_file';
  params: Required<Pick<Record<ToolParamName, string>, 'path' | 'search' | 'replace'>> &
    Partial<Pick<Record<ToolParamName, string>, 'use_regex' | 'ignore_case' | 'start_line' | 'end_line' | 'diff'>>;
}

export interface SearchFilesToolUse extends ToolUse {
  name: 'use_search_files';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'regex' | 'file_pattern'>>;
}

export interface ListFilesToolUse extends ToolUse {
  name: 'use_list_files';
  params: Partial<Pick<Record<ToolParamName, string>, 'path' | 'recursive'>>;
}

export interface ListCodeDefinitionNamesToolUse extends ToolUse {
  name: 'use_definition_names';
  params: Partial<Pick<Record<ToolParamName, string>, 'path'>>;
}

export interface UseBrowserToolUse extends ToolUse {
  name: 'use_browser';
  params: Required<Pick<Record<ToolParamName, string>, 'action'>> &
    Partial<Pick<Record<ToolParamName, string>, 'url' | 'coordinate' | 'text' | 'size' | 'duration'>>;
}

export interface UseMcpToolToolUse extends ToolUse {
  name: 'use_mcp_tools';
  params: Required<Pick<Record<ToolParamName, string>, 'server_name'>> &
    Partial<Pick<Record<ToolParamName, string>, 'tool_name' | 'arguments'>>;
}

export interface AccessMcpResourceToolUse extends ToolUse {
  name: 'get_mcp_resource';
  params: Required<Pick<Record<ToolParamName, string>, 'server_name'>> &
    Partial<Pick<Record<ToolParamName, string>, 'uri'>>;
}

export interface GetUserQuestionToolUse extends ToolUse {
  name: 'get_user_question';
  params: Partial<Pick<Record<ToolParamName, string>, 'question'>>;
}
export interface UseWebSearchToolUse extends ToolUse {
  name: 'use_web_search';
  params: Partial<Pick<Record<ToolParamName, string>, 'query'>>;
}

export interface UseCodeBaseToolUse extends ToolUse {
  name: 'use_codebase';
  params: Partial<Pick<Record<ToolParamName, string>, 'query'>>;
}

export interface CodebaseSearchToolUse extends ToolUse {
  name: 'codebase_search';
  params: Partial<Pick<Record<ToolParamName, string>, 'query' | 'path'>>;
}

export interface ClearPublishToolUse extends ToolUse {
  name: 'use_clear_publish';
  params: Required<Pick<Record<ToolParamName, string>, 'project_path' | 'project_name'>>;
}

export interface AttemptCompletionToolUse extends ToolUse {
  name: 'attempt_task_done';
  params: Partial<Pick<Record<ToolParamName, string>, 'result' | 'command'>>;
}

export interface SwitchModeToolUse extends ToolUse {
  name: 'switch_mode';
  params: Partial<Pick<Record<ToolParamName, string>, 'mode_slug' | 'reason'>>;
}
export interface NewTaskToolUse extends ToolUse {
  name: 'new_task_creation';
  params: Partial<Pick<Record<ToolParamName, string>, 'mode' | 'message'>>;
}
export interface NewTaskWithCondenseContextToolUse extends ToolUse {
  name: 'new_task_with_condense_context';
  params: Required<Pick<Record<ToolParamName, string>, 'context'>>;
}

export interface AskFollowupQuestionToolUse extends ToolUse {
  name: 'ask_followup_question';
  params: Required<Pick<Record<ToolParamName, string>, 'question'>> &
    Partial<Pick<Record<ToolParamName, string>, 'follow_up'>>;
}

export interface FetchInstructionsToolUse extends ToolUse {
  name: 'fetch_instructions';
  params: Partial<Pick<Record<ToolParamName, string>, 'task'>>;
}
