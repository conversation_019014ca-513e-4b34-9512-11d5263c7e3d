import '../../utils/path'; // Import to ensure String.prototype.toPosix is available
import delay from 'delay';
import os from 'os';
import pWaitFor from 'p-wait-for';
import * as path from 'path';
import * as vscode from 'vscode';

import { listFiles } from '../../services/glob/list-files';
import { findLast } from '../../shared/array';
import { combineApiRequests } from '../../shared/combineApiRequests';
import { combineCommandSequences } from '../../shared/combineCommandSequences';
import { JoyCoderMessage } from '../../shared/ExtensionMessage';
import { arePathsEqual } from '../../utils/path';
import { formatResponse } from '../prompts/responses';
import { getContextWindowInfo } from '../context-management/context-window-utils';
import { FileSystemHelper } from '../../utils/FileSystemHelper';
import { experiments as Experiments } from '../../schemas';
import { defaultModeSlug, getModeBySlug } from '../../../web-agent/src/utils/modes';
import { EXPERIMENT_IDS, getFullModeDetails, isToolAllowedForMode } from '../../shared/modes';
import { formatLanguage } from '../../shared/language';
import { JoyCoder } from '../Joycoder';
import { isIDE } from '@joycoder/shared';

export async function getEnvironmentDetails(
  jc: JoyCoder,
  cwd: string | vscode.Uri,
  includeFileDetails: boolean = false
) {
  let details = '';

  // It could be useful for JoyCoder to know if the user went from one or no file to another between messages, so we always include this context
  details += isIDE() ? '\n\n# JoyCode Files' : '\n\n# VSCode Visible Files';
  const visibleFilePaths = vscode.window.visibleTextEditors
    ?.map((editor) => editor.document?.uri?.fsPath)
    .filter(Boolean)
    .map((absolutePath) => FileSystemHelper.relative(cwd, absolutePath));

  // Filter paths through JoyCoderIgnoreController
  const allowedVisibleFiles = jc.JoyCoderIgnoreController.filterPaths(visibleFilePaths)
    .map((p: string) => p.toPosix())
    .join('\n');

  if (allowedVisibleFiles) {
    details += `\n${allowedVisibleFiles}`;
  } else {
    details += '\n(No visible files)';
  }

  details += isIDE() ? '\n\n# JoyCode Open Tabs' : '\n\n# VSCode Open Tabs';
  const openTabPaths = vscode.window.tabGroups.all
    .flatMap((group) => group.tabs)
    .map((tab) => (tab.input as vscode.TabInputText)?.uri?.fsPath)
    .filter(Boolean)
    .map((absolutePath) => FileSystemHelper.relative(cwd, absolutePath));

  // Filter paths through JoyCoderIgnoreController
  const allowedOpenTabs = jc.JoyCoderIgnoreController.filterPaths(openTabPaths)
    .map((p: string) => p.toPosix())
    .join('\n');

  if (allowedOpenTabs) {
    details += `\n${allowedOpenTabs}`;
  } else {
    details += '\n(No open tabs)';
  }

  const busyTerminals = jc.terminalManager.getTerminals(true);
  const inactiveTerminals = jc.terminalManager.getTerminals(false);
  // const allTerminals = [...busyTerminals, ...inactiveTerminals]

  if (busyTerminals.length > 0 && jc.didEditFile) {
    //  || jc.didEditFile
    await delay(300); // delay after saving file to let terminals catch up
  }

  // let terminalWasBusy = false
  if (busyTerminals.length > 0) {
    // wait for terminals to cool down
    // terminalWasBusy = allTerminals.some((t) => jc.terminalManager.isProcessHot(t.id))
    await pWaitFor(() => busyTerminals.every((t) => !jc.terminalManager.isProcessHot(t.id)), {
      interval: 100,
      timeout: 15_000,
    }).catch(() => {});
  }

  jc.didEditFile = false; // reset, this lets us know when to wait for saved files to update terminals

  // waiting for updated diagnostics lets terminal output be the most up-to-date possible
  let terminalDetails = '';
  if (busyTerminals.length > 0) {
    // terminals are cool, let's retrieve their output
    terminalDetails += '\n\n# Actively Running Terminals';
    for (const busyTerminal of busyTerminals) {
      terminalDetails += `\n## Original command: \`${busyTerminal.lastCommand}\``;
      const newOutput = jc.terminalManager.getUnretrievedOutput(busyTerminal.id);
      if (newOutput) {
        terminalDetails += `\n### New Output\n${newOutput}`;
      }
    }
  }
  // only show inactive terminals if there's output to show
  if (inactiveTerminals.length > 0) {
    const inactiveTerminalOutputs = new Map<number, string>();
    for (const inactiveTerminal of inactiveTerminals) {
      const newOutput = jc.terminalManager.getUnretrievedOutput(inactiveTerminal.id);
      if (newOutput) {
        inactiveTerminalOutputs.set(inactiveTerminal.id, newOutput);
      }
    }
    if (inactiveTerminalOutputs.size > 0) {
      terminalDetails += '\n\n# Inactive Terminals';
      for (const [terminalId, newOutput] of inactiveTerminalOutputs) {
        const inactiveTerminal = inactiveTerminals.find((t) => t.id === terminalId);
        if (inactiveTerminal) {
          terminalDetails += `\n## ${inactiveTerminal.lastCommand}`;
          terminalDetails += `\n### New Output\n${newOutput}`;
        }
      }
    }
  }

  if (terminalDetails) {
    details += terminalDetails;
  }

  // Add recently modified files section
  const recentlyModifiedFiles = jc.fileContextTracker.getAndClearRecentlyModifiedFiles();
  if (recentlyModifiedFiles.length > 0) {
    details +=
      '\n\n# Recently Modified Files\nThese files have been modified since you last accessed them (file was just edited so you may need to re-read it before editing):';
    for (const filePath of recentlyModifiedFiles) {
      details += `\n${filePath}`;
    }
  }

  // Add current time information with timezone
  const now = new Date();
  const formatter = new Intl.DateTimeFormat(undefined, {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    hour12: true,
  });
  const timeZone = formatter.resolvedOptions().timeZone;
  const timeZoneOffset = -now.getTimezoneOffset() / 60; // Convert to hours and invert sign to match conventional notation
  const timeZoneOffsetStr = `${timeZoneOffset >= 0 ? '+' : ''}${timeZoneOffset}:00`;
  details += `\n\n# Current Time\n${formatter.format(now)} (${timeZone}, UTC${timeZoneOffsetStr})`;

  // Add current mode and any mode-specific warnings.
  const {
    mode,
    customModes,
    customModePrompts,
    customInstructions: globalCustomInstructions,
    apiConfiguration,
    diffEnabled,
  } = (await jc.providerRef.deref()?.getState()) || {};

  const currentMode = mode ?? defaultModeSlug;

  const modeDetails = await getFullModeDetails(currentMode, customModes, customModePrompts, {
    cwd: FileSystemHelper.getRemotePath(jc.cwd),
    globalCustomInstructions,
    language: formatLanguage(vscode.env.language),
  });

  details += `\n\n# Current Mode\n`;
  details += `<agentId>${currentMode}</agentId>\n`;
  details += `<name>${modeDetails.name}</name>\n`;
  details += `<model>${apiConfiguration?.apiModelId}</model>\n`;
  if (Experiments.isEnabled({ powerSteering: true }, EXPERIMENT_IDS.POWER_STEERING)) {
    details += `<role>${modeDetails.agentDefinition}</role>\n`;

    if (modeDetails.customInstructions) {
      details += `<custom_instructions>${modeDetails.customInstructions}</custom_instructions>\n`;
    }
  }

  // Add warning if not in code mode.
  if (
    !isToolAllowedForMode('use_write_file', currentMode, customModes ?? [], { apply_diff: !!diffEnabled }) &&
    !isToolAllowedForMode('apply_diff', currentMode, customModes ?? [], { apply_diff: !!diffEnabled })
  ) {
    const currentModeName = getModeBySlug(currentMode, customModes)?.name ?? currentMode;
    const defaultModeName = getModeBySlug(defaultModeSlug, customModes)?.name ?? defaultModeSlug;
    details += `\n\nNOTE: You are currently in '${currentModeName}' mode, which does not allow write operations. To write files, the user will need to switch to a mode that supports file writing, such as '${defaultModeName}' mode.`;
  }

  if (includeFileDetails) {
    details += `\n\n# Current Working Directory (${FileSystemHelper.getRemotePath(jc.cwd).toPosix()}) Files\n`;
    const isDesktop = arePathsEqual(FileSystemHelper.getRemotePath(jc.cwd), path.join(os.homedir(), 'Desktop'));
    if (isDesktop) {
      // don't want to immediately access desktop since it would show permission popup
      details += '(Desktop files not shown automatically. Use use_list_files to explore if needed.)';
    } else {
      const [files, didHitLimit] = await listFiles(cwd, true, 200);
      const result = formatResponse.formatFilesList(cwd, files, didHitLimit, jc.JoyCoderIgnoreController);
      details += result;
    }
  }

  // Add context window usage information
  const { contextWindow, maxAllowedSize } = getContextWindowInfo(jc.api);
  // Get the token count from the most recent API request to accurately reflect context management
  const getTotalTokensFromApiReqMessage = (msg: JoyCoderMessage) => {
    if (!msg.text) {
      return 0;
    }
    try {
      const { tokensIn, tokensOut, cacheWrites, cacheReads } = JSON.parse(msg.text);
      return (tokensIn || 0) + (tokensOut || 0) + (cacheWrites || 0) + (cacheReads || 0);
    } catch (e) {
      return 0;
    }
  };

  const modifiedMessages = combineApiRequests(combineCommandSequences(jc.JoyCoderMessages.slice(1)));
  const lastApiReqMessage = findLast(modifiedMessages, (msg) => {
    if (msg.say !== 'api_req_started') {
      return false;
    }
    return getTotalTokensFromApiReqMessage(msg) > 0;
  });

  const lastApiReqTotalTokens = lastApiReqMessage ? getTotalTokensFromApiReqMessage(lastApiReqMessage) : 0;
  const usagePercentage = Math.round((lastApiReqTotalTokens / contextWindow) * 100);
  details += '\n# Context Window Usage';
  details += `\n${lastApiReqTotalTokens.toLocaleString()} / ${(
    contextWindow / 1000
  ).toLocaleString()}K tokens used (${usagePercentage}%)`;

  return `<environment_details>\n${details.trim()}\n</environment_details>`;
}
