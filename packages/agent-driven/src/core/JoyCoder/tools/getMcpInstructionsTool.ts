import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { loadMcpDocumentation } from '../../prompts/loadMcpDocumentation';
import { pushToolResult, handleError } from './common';

export async function getMcpInstructionsTool(joyCoder: JoyCoder, block: ToolUse) {
  try {
    if (block.partial) {
      return;
    } else {
      await joyCoder.say('get_mcp_instructions', '', undefined, false);
      pushToolResult(joyCoder, block, await loadMcpDocumentation(joyCoder.providerRef.deref()?.mcpHub));
      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, 'loading MCP documentation', error);
    return;
  }
}
