// Core Node.js imports
import delay from 'delay';

// Internal imports

import AdoptResultCache from '@joycoder/plugin-base-code-completion/src/stats/adoptResultCache';
import { ActionType, WorkspaceState, reportAction } from '@joycoder/shared';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { fileExistsAtPath } from '../../../utils/fs';
import { getReadablePath } from '../../../utils/path';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import { removeClosingTag, pushToolResult, handleError } from './common';
import { XMLBuilder } from 'fast-xml-parser';
import { FileSystemHelper } from '../../../utils/FileSystemHelper';

/**
 * Tool for performing search and replace operations on files
 * Supports regex and case-sensitive/insensitive matching
 */

/**
 * Validates required parameters for search and replace operation
 */
async function validateParams(
  joycoder: JoyCoder,
  block: ToolUse,
  relPath: string | undefined,
  search: string | undefined,
  replace: string | undefined
): Promise<boolean> {
  if (!relPath) {
    joycoder.consecutiveMistakeCount++;
    await pushToolResult(joycoder, block, await joycoder.sayAndCreateMissingParamError('use_replace_file', 'path'));
    await joycoder.saveCheckpoint();
    return false;
  }

  if (!search) {
    joycoder.consecutiveMistakeCount++;
    await pushToolResult(joycoder, block, await joycoder.sayAndCreateMissingParamError('use_replace_file', 'search'));
    await joycoder.saveCheckpoint();
    return false;
  }

  if (replace === undefined) {
    joycoder.consecutiveMistakeCount++;
    await pushToolResult(joycoder, block, await joycoder.sayAndCreateMissingParamError('use_replace_file', 'replace'));
    await joycoder.saveCheckpoint();
    return false;
  }

  return true;
}

/**
 * Performs search and replace operations on a file
 * @param joycoder - JoyCoder instance
 * @param block - Tool use parameters
 * @param askApproval - Function to request user approval
 * @param handleError - Function to handle errors
 * @param pushToolResult - Function to push tool results
 * @param removeClosingTag - Function to remove closing tags
 */
export async function useReplaceFileTool(joycoder: JoyCoder, block: ToolUse): Promise<void> {
  // Extract and validate parameters
  const relPath: string | undefined = block.params.path;
  const search: string | undefined = block.params.search;
  const replace: string | undefined = block.params.replace;
  const useRegex: boolean = block.params.use_regex === 'true';
  const ignoreCase: boolean = block.params.ignore_case === 'true';
  const startLine: number | undefined = block.params.start_line ? parseInt(block.params.start_line, 10) : undefined;
  const endLine: number | undefined = block.params.end_line ? parseInt(block.params.end_line, 10) : undefined;
  const userContent = block.userContent;
  const conversationId: string | undefined = joycoder.conversationId;

  try {
    // Handle partial tool use
    if (block.partial) {
      const partialMessageProps = {
        tool: 'searchAndReplace' as const,
        path: getReadablePath(joycoder.cwd, removeClosingTag(joycoder, 'path', relPath, block.partial)),
        search: removeClosingTag(joycoder, 'search', search, block.partial),
        replace: removeClosingTag(joycoder, 'replace', replace, block.partial),
        useRegex: block.params.use_regex === 'true',
        ignoreCase: block.params.ignore_case === 'true',
        startLine,
        endLine,
      };
      await joycoder.ask('tool', JSON.stringify(partialMessageProps), block.partial).catch(() => {});
      return;
    }

    // Validate required parameters
    if (!(await validateParams(joycoder, block, relPath, search, replace))) {
      return;
    }

    // At this point we know relPath, search and replace are defined
    const validRelPath = relPath as string;
    const validSearch = search as string;
    const validReplace = replace as string;

    const sharedMessageProps: JoyCoderSayTool = {
      tool: 'searchAndReplace',
      path: getReadablePath(joycoder.cwd, validRelPath),
      search: validSearch,
      conversationId,
      replace: validReplace,
      useRegex: useRegex,
      ignoreCase: ignoreCase,
      startLine: startLine,
      endLine: endLine,
    };
    const accessAllowed = joycoder.JoyCoderIgnoreController?.validateAccess(validRelPath);

    if (!accessAllowed) {
      await joycoder.say('joycoderignore_error', validRelPath);
      pushToolResult(joycoder, block, formatResponse.toolError(formatResponse.joycoderIgnoreError(validRelPath)));
      await joycoder.saveCheckpoint();
      return;
    }

    const absolutePath = FileSystemHelper.resolveUri(joycoder.cwd, validRelPath);
    const fileExists = await fileExistsAtPath(absolutePath);

    if (!fileExists) {
      joycoder.consecutiveMistakeCount++;
      const displayPath = FileSystemHelper.getRemotePath(absolutePath);
      const formattedError = formatResponse.toolError(
        `File does not exist at path: ${displayPath}\nThe specified file could not be found. Please verify the file path and try again.`
      );
      await joycoder.say('error', formattedError);
      await pushToolResult(joycoder, block, formattedError);
      await joycoder.saveCheckpoint();
      return;
    }

    // Reset consecutive mistakes since all validations passed
    joycoder.consecutiveMistakeCount = 0;

    // Read and process file content
    let fileContent: string;
    try {
      fileContent = await FileSystemHelper.readFile(absolutePath, 'utf-8');
    } catch (error) {
      joycoder.consecutiveMistakeCount++;
      const displayPath = FileSystemHelper.getRemotePath(absolutePath);
      const errorMessage = `Error reading file: ${displayPath}\nFailed to read the file content: ${
        error instanceof Error ? error.message : String(error)
      }\nPlease verify file permissions and try again.`;
      const formattedError = formatResponse.toolError(errorMessage);
      await joycoder.say('error', formattedError);
      await pushToolResult(joycoder, block, formattedError);
      await joycoder.saveCheckpoint();
      return;
    }

    // Create search pattern and perform replacement
    const flags = ignoreCase ? 'gi' : 'g';
    const searchPattern = useRegex ? new RegExp(validSearch, flags) : new RegExp(escapeRegExp(validSearch), flags);

    let newContent: string;
    if (startLine !== undefined || endLine !== undefined) {
      // Handle line-specific replacement
      const lines = fileContent.split('\n');
      const start = Math.max((startLine ?? 1) - 1, 0);
      const end = Math.min((endLine ?? lines.length) - 1, lines.length - 1);

      // Get content before and after target section
      const beforeLines = lines.slice(0, start);
      const afterLines = lines.slice(end + 1);

      // Get and modify target section
      const targetContent = lines.slice(start, end + 1).join('\n');
      const modifiedContent = targetContent.replace(/\r\n/g, '\n').replace(searchPattern, validReplace);
      const modifiedLines = modifiedContent.split('\n');

      // Reconstruct full content
      newContent = [...beforeLines, ...modifiedLines, ...afterLines].join('\n');
    } else {
      // Global replacement
      fileContent = fileContent.replace(/\r\n/g, '\n');
      newContent = fileContent.replace(searchPattern, validReplace);
    }

    // Initialize diff view
    joycoder.diffViewProvider.editType = 'modify';
    joycoder.diffViewProvider.originalContent = fileContent;

    // Generate and validate diff
    const diff = formatResponse.createPrettyPatch(validRelPath, fileContent, newContent);
    if (!diff) {
      await pushToolResult(joycoder, block, `No changes needed for '${relPath}'`);
      await joycoder.diffViewProvider.reset();
      await joycoder.saveCheckpoint();
      return;
    }

    try {
      reportAction({
        actionCate: 'ai',
        actionType: ActionType.AutoGenerator,
        question: userContent?.map((item: any) => item?.text)?.join('\n'),
        result: diff || newContent,
        conversationId,
        model: WorkspaceState.get('openAiModelId'),
        startTime: new Date(),
        extendMsg: {
          type: 'yesButtonClicked',
        },
      });
      AdoptResultCache.setRemote(
        newContent,
        WorkspaceState.get('openAiModelId'),
        AdoptResultCache.ADOPT_CODE_SOURCE.AUTO_CODE,
        joycoder.conversationId
      );
    } catch (error) {
      console.error('%c [ reportAction->error ]-1881', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }

    // Show changes in diff view
    if (!joycoder.diffViewProvider.isEditing) {
      await joycoder.ask('tool', JSON.stringify(sharedMessageProps), true).catch(() => {});
      await joycoder.diffViewProvider.open(validRelPath);
      await joycoder.diffViewProvider.update(fileContent, false);
      joycoder.diffViewProvider.scrollToFirstDiff();
      await delay(200);
    }

    await joycoder.diffViewProvider.update(newContent, true);

    // Request user approval for changes
    const completeMessage = JSON.stringify({ ...sharedMessageProps, diff } satisfies JoyCoderSayTool);
    if (!joycoder.shouldAutoApproveTool(block.name)) {
      const didApprove = await joycoder
        .ask('tool', completeMessage, false)
        .then((response) => response.response === 'yesButtonClicked');
      if (!didApprove) {
        await joycoder.diffViewProvider.revertChanges();
        await pushToolResult(joycoder, block, 'Changes were rejected by the user.');
        await joycoder.diffViewProvider.reset();
        await joycoder.saveCheckpoint();
        return;
      }
    } else {
      // 自动批准：发送包含 diff 的完整消息
      joycoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
      await joycoder.say('tool', completeMessage, undefined, false);
      joycoder.consecutiveAutoApprovedRequestsCount++;
    }
    // 用户采纳上报
    try {
      reportAction({
        actionCate: 'ai',
        actionType: ActionType.copy,
        question: userContent?.map((item: any) => item?.text)?.join('\n'),
        result: '', //此处惯例不上报内容，数据侧有处理逻辑认定为全部采纳newContent,
        conversationId,
        model: WorkspaceState.get('openAiModelId'),
        startTime: new Date(),
        extendMsg: {
          type: 'yesButtonClicked',
          modeType: ((await joycoder.providerRef.deref()?.getState()) || {}).mode,
          taskId: joycoder.taskId,
          sessionId: joycoder.sessionId,
        },
      });
    } catch (error) {
      console.error(
        '%c [ askApproval-reportAction-error ]-1946',
        'font-size:13px; background:pink; color:#bf2c9f;',
        error
      );
    }
    AdoptResultCache.clearAutoCodeAdopt();
    const { newProblemsMessage, userEdits, finalContent } = await joycoder.diffViewProvider.saveChanges();

    // Track file edit operation
    if (relPath) {
      await joycoder.fileContextTracker.trackFileContext(relPath, 'JoyCoder_edited');
    }

    joycoder.didEditFile = true;

    // Only send user_feedback_diff if userEdits exists
    if (userEdits) {
      // Create say object for UI feedback
      const say: JoyCoderSayTool = {
        tool: 'editedExistingFile',
        path: getReadablePath(joycoder.cwd, relPath),
        diff: userEdits,
      };

      // Send the user feedback
      await joycoder.say('user_feedback_diff', JSON.stringify(say));
    }

    // Build XML response
    const xmlObj = {
      file_write_result: {
        path: relPath,
        operation: 'modified',
        user_edits: userEdits ? userEdits : undefined,
        problems: newProblemsMessage || undefined,
        notice: {
          i: [
            'You do not need to re-read the file, as you have seen all changes',
            'Proceed with the task using these changes as the new baseline.',
            ...(userEdits
              ? [
                  "If the user's edits have addressed part of the task or changed the requirements, adjust your approach accordingly.",
                ]
              : []),
          ],
        },
      },
    };

    const builder = new XMLBuilder({
      format: true,
      indentBy: '',
      suppressEmptyNode: true,
      processEntities: false,
      tagValueProcessor: (name, value) => {
        if (typeof value === 'string') {
          // Only escape <, >, and & characters
          return value.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
        }
        return value;
      },
      attributeValueProcessor: (name, value) => {
        if (typeof value === 'string') {
          // Only escape <, >, and & characters
          return value.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
        }
        return value;
      },
    });

    const resultMessage = builder.build(xmlObj);

    await pushToolResult(joycoder, block, resultMessage);

    // Record successful tool usage and cleanup
    await joycoder.diffViewProvider.reset();
    await joycoder.saveCheckpoint();
  } catch (error) {
    handleError(joycoder, block, '文件搜索替换', error);
    await joycoder.diffViewProvider.revertChanges();
    await joycoder.diffViewProvider.reset();
    await joycoder.saveCheckpoint();
  }
}

/**
 * Escapes special regex characters in a string
 * @param input String to escape regex characters in
 * @returns Escaped string safe for regex pattern matching
 */
function escapeRegExp(input: string): string {
  return input.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
