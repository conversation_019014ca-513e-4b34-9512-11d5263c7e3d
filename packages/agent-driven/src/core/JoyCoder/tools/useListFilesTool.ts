import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { listFiles } from '../../../services/glob/list-files';
import { JoyCoderSayTool } from '../../../shared/ExtensionMessage';
import { FileSystemHelper } from '../../../utils/FileSystemHelper';
import { getReadablePath } from '../../../utils/path';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import {
  removeClosingTag,
  pushToolResult,
  showNotificationForApprovalIfAutoApprovalEnabled,
  askApproval,
  handleError,
} from './common';

export async function useListFilesTool(joyCoder: JoyCoder, block: ToolUse) {
  const relDirPath: string | undefined = block.params.path;
  const recursiveRaw: string | undefined = block.params.recursive;
  const recursive = recursiveRaw?.toLowerCase() === 'true';
  const sharedMessageProps: JoyCoderSayTool = {
    tool: !recursive ? 'listFilesTopLevel' : 'listFilesRecursive',
    path: getReadablePath(joyCoder.cwd, removeClosingTag(joyCoder, 'path', relDirPath, block.partial)),
  };
  try {
    if (block.partial) {
      const partialMessage = JSON.stringify({
        ...sharedMessageProps,
        content: '',
      } as JoyCoderSayTool);
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', partialMessage, undefined, block.partial);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        await joyCoder.ask('tool', partialMessage, block.partial).catch(() => {});
      }
      return;
    } else {
      if (!relDirPath) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(joyCoder, block, await joyCoder.sayAndCreateMissingParamError('use_list_files', 'path'));

        return;
      }
      joyCoder.consecutiveMistakeCount = 0;
      // const absolutePath = vscode.Uri.joinPath(cwd, relDirPath);
      const absolutePath = FileSystemHelper.resolveUri(joyCoder.cwd, relDirPath);
      const [files, didHitLimit] = await listFiles(absolutePath, recursive, 200);

      const result = formatResponse.formatFilesList(
        absolutePath,
        files,
        didHitLimit,
        joyCoder.JoyCoderIgnoreController
      );
      const completeMessage = JSON.stringify({
        ...sharedMessageProps,
        content: result,
      } as JoyCoderSayTool);
      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'tool');
        await joyCoder.say('tool', completeMessage, undefined, false);
        joyCoder.consecutiveAutoApprovedRequestsCount++;
      } else {
        showNotificationForApprovalIfAutoApprovalEnabled(
          joyCoder,
          `JoyCode 想要查看目录 ${FileSystemHelper.basename(absolutePath)}/`
        );
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'tool');
        const didApprove = await askApproval(
          joyCoder,
          block,
          'tool',
          completeMessage,
          joyCoder.conversationId,
          block.userContent
        );
        if (!didApprove) {
          await joyCoder.saveCheckpoint();
          return;
        }
      }
      await pushToolResult(joyCoder, block, result);

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, JoyCoderRestoreMessageMap['listing files'], error);

    return;
  }
}
