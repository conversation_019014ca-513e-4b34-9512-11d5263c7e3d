import { JoyCoderRestoreMessageMap } from '../../../adaptor/translate/message';
import { JoyCoderAskUseMcpServer } from '../../../shared/ExtensionMessage';
import { ToolUse } from '../../assistant-message';
import { JoyCoder } from '../../Joycoder';
import { formatResponse } from '../../prompts/responses';
import {
  removeClosingTag,
  pushToolResult,
  showNotificationForApprovalIfAutoApprovalEnabled,
  askApproval,
  handleError,
} from './common';

export async function useMcpTools(joyCoder: JoyCoder, block: ToolUse) {
  const server_name: string | undefined = block.params.server_name;
  const tool_name: string | undefined = block.params.tool_name;
  const mcp_arguments: string | undefined = block.params.arguments;
  try {
    if (block.partial) {
      const partialMessage = JSON.stringify({
        type: 'use_mcp_tools',
        serverName: removeClosingTag(joyCoder, 'server_name', server_name, block.partial),
        toolName: removeClosingTag(joyCoder, 'tool_name', tool_name, block.partial),
        arguments: removeClosingTag(joyCoder, 'arguments', mcp_arguments, block.partial),
      } as JoyCoderAskUseMcpServer);

      if (joyCoder.shouldAutoApproveTool(block.name)) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'use_mcp_server');
        await joyCoder.say('use_mcp_server', partialMessage, undefined, block.partial);
      } else {
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'use_mcp_server');
        await joyCoder.ask('use_mcp_server', partialMessage, block.partial).catch(() => {});
      }

      return;
    } else {
      if (!server_name) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('use_mcp_tools', 'server_name')
        );
        await joyCoder.saveCheckpoint();
        return;
      }
      if (!tool_name) {
        joyCoder.consecutiveMistakeCount++;
        await pushToolResult(
          joyCoder,
          block,
          await joyCoder.sayAndCreateMissingParamError('use_mcp_tools', 'tool_name')
        );
        await joyCoder.saveCheckpoint();
        return;
      }
      // arguments are optional, but if they are provided they must be valid JSON
      // if (!mcp_arguments) {
      // 	joyCoder.consecutiveMistakeCount++
      // 	await pushToolResult(joyCoder,block,await joyCoder.sayAndCreateMissingParamError("use_mcp_tools", "arguments"))
      // 	break
      // }
      let parsedArguments: Record<string, unknown> | undefined;
      if (mcp_arguments) {
        try {
          parsedArguments = JSON.parse(mcp_arguments);
        } catch (error) {
          joyCoder.consecutiveMistakeCount++;
          await joyCoder.say(
            'error',
            // `JoyCoder tried to use ${tool_name} with an invalid JSON argument. Retrying...`,
            `JoyCode 尝试使用 ${tool_name}，但使用了无效的 JSON 参数。正在重试...`
          );
          await pushToolResult(
            joyCoder,
            block,
            formatResponse.toolError(formatResponse.invalidMcpToolArgumentError(server_name, tool_name))
          );
          await joyCoder.saveCheckpoint();
          return;
        }
      }
      joyCoder.consecutiveMistakeCount = 0;
      const completeMessage = JSON.stringify({
        type: 'use_mcp_tools',
        serverName: server_name,
        toolName: tool_name,
        arguments: mcp_arguments,
      } as JoyCoderAskUseMcpServer);

      const isToolAutoApproved = joyCoder.providerRef
        .deref()
        ?.mcpHub?.connections?.find((conn: { server: { name: string } }) => conn.server.name === server_name)
        ?.server.tools?.find((tool: { name: string }) => tool.name === tool_name)?.autoApprove;

      if (joyCoder.shouldAutoApproveTool(block.name) && isToolAutoApproved) {
        joyCoder.removeLastPartialMessageIfExistsWithType('ask', 'use_mcp_server');
        await joyCoder.say('use_mcp_server', completeMessage, undefined, false);
        joyCoder.consecutiveAutoApprovedRequestsCount++;
      } else {
        // showNotificationForApprovalIfAutoApprovalEnabled(joyCoder, `JoyCode 想在 ${server_name} 上使用 ${tool_name}`);
        joyCoder.removeLastPartialMessageIfExistsWithType('say', 'use_mcp_server');
        const didApprove = await askApproval(
          joyCoder,
          block,
          'use_mcp_server',
          completeMessage,
          joyCoder.conversationId,
          block.userContent
        );
        if (!didApprove) {
          await joyCoder.saveCheckpoint();
          return;
        }
      }
      // now execute the tool
      await joyCoder.say('mcp_server_request_started'); // same as use_browser_result
      let toolResult;
      try {
        toolResult = await joyCoder.providerRef.deref()?.mcpHub?.callTool(server_name, tool_name, parsedArguments);
      } catch (error) {
        await pushToolResult(
          joyCoder,
          block,
          `The MCP server's server_name retrieval has failed. Please inform the user about the current error, then when re-obtaining the correct server_name, pay attention to case sensitivity and ensure proper MCP connection is established.`
        );
        return;
      }

      // TODO: add progress indicator and ability to parse images and non-text responses
      const toolResultPretty =
        (toolResult?.isError ? 'Error:\n' : '') +
          toolResult?.content
            .map((item) => {
              if (item.type === 'text') {
                return item.text;
              }
              if (item.type === 'resource') {
                const { blob, ...rest } = item.resource;
                return JSON.stringify(rest, null, 2);
              }
              return '';
            })
            .filter(Boolean)
            .join('\n\n') || '(No response)';
      await joyCoder.say('mcp_server_response', toolResultPretty);
      await pushToolResult(joyCoder, block, formatResponse.toolResult(toolResultPretty));

      await joyCoder.saveCheckpoint();

      return;
    }
  } catch (error) {
    await handleError(joyCoder, block, JoyCoderRestoreMessageMap['executing MCP tool'], error);
    return;
  }
}
