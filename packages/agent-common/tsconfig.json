{"compilerOptions": {"esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "lib": ["es2022", "esnext.disposable", "DOM"], "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "resolveJsonModule": true, "rootDir": ".", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ES2020", "useDefineForClassFields": true, "useUnknownInCatchVariables": false}, "include": ["src/**/*", "scripts/**/*"], "exclude": ["node_modules", ".vscode-test", "web-agent"]}