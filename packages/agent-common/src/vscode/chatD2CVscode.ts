import ChatJoyCoder from './chatJoyCoder';
import h5GenAgent from '../h5GenAgent';
import { saveFiles } from './fileUtil';

export default function chatD2CVscode(html: string, options?: Record<string, any>) {
  const model = new ChatJoyCoder({
    temperature: 0.1,
    apiKey: 'empty',
  });
  options = {
    ...options,
    generateFiles: async (fileList: any) => {
      return await saveFiles(fileList);
    },
  };
  const htmlResult = h5GenAgent(html, model, options);
  return htmlResult;
}
