//vscode入口
import { ChatOpenAI } from '@langchain/openai';
import { type ClientOptions } from 'openai';
import { type BaseChatModelParams } from '@langchain/core/language_models/chat_models';
import { OpenAIChatInput } from '@langchain/openai';
import { forceJdhLogin, getJdhLoginInfo, hasJdhLoginCookie } from '@joycoder/shared';
import { type BaseMessage } from '@langchain/core/messages';
import { CallbackManagerForLLMRun } from '@langchain/core/callbacks/manager';
import { type ChatResult } from '@langchain/core/outputs';
import { getChatModelAndConfig } from '@joycoder/plugin-base-ai/src/model';
import { DevConsole } from '../utils';

/**
 * JoyCoder带登录态的OpenAI格式模型请求类
 * api文档
 * https://joyspace.jd.com/pages/x5Z1ozZIzmjXMZWwh3v0
 */
export interface IExtParams {
  bizId: string;
  bizToken: string;
  ssoClientId: string;
  jdhLoginParams: {
    userToken?: string;
    userName?: string;
    erp?: string;
    sourceType: string;
  };
}

/**
 * 获取OpenAI定制接口的扩展参数
 * @returns IExtParams
 */
export function getExtParams(): IExtParams {
  return {
    bizId: 'joycoderfe',
    bizToken: 'd99fa9a5-3655-4251-8141-e0f84dccb099',
    ssoClientId: 'SSO1.0',
    jdhLoginParams: { ...getJdhLoginInfo(), sourceType: 'joyCoderFe' },
  };
}

export type IChatModelOptions = Partial<OpenAIChatInput> &
  BaseChatModelParams & {
    configuration?: ClientOptions;
  };

/**
 * 定义一个扩展自ChatOpenAI的类ChatJoyCoder，用于JoyCoder服务的聊天模型。
 *
 * 构造函数接收可选参数`fields`，用于初始化模型配置。
 * - `openAIApiKey`设置为非空字符串以绕过内部校验。
 * - `streaming`默认为`true`，启用流式传输。
 * - `modelName`根据提供的名称使用相应的模型配置。
 * - `modelKwargs`中的`extParams`包含透传给接口的扩展参数。
 * - `configuration`中的`baseURL`设置为JoyCoder服务的基础URL。
 * - `maxRetries`默认为0，禁用重试。
 *
 * `_generate`方法在生成响应前，校验并更新登录状态和参数。
 */
export default class ChatJoyCoder extends ChatOpenAI {
  _llmType(): string {
    return 'joycoder';
  }
  modelKwargs: OpenAIChatInput['modelKwargs'];

  /**
   * 构造函数，初始化Chat模型配置。
   * @param fields 可选参数，包含模型配置和其他选项。
   * - 如果提供`fields`，将使用其值初始化新字段。
   * - 如果未提供`fields`，将使用默认值。
   * - `openAIApiKey`被设置为非空字符串以绕过内部校验。
   * - `streaming`默认为`true`，表示流式传输。
   * - `modelName`根据提供的名称使用相应的模型配置。
   * - `modelKwargs`中的`extParams`包含透传给接口的扩展参数。
   * - 如果模型配置中包含`chatApiUrl`，则会设置到`extParams.apiUrl`中。
   * - `configuration`中的`baseURL`被设置为服务的基础URL。
   * - `maxRetries`默认为0，禁用默认的重试次数。
   */
  constructor(fields?: IChatModelOptions) {
    const newFields = fields ? { ...fields } : {};
    //JoyCoder服务端
    const BASE_URL = 'http://chatgpt-relay.jd.com/v1/';
    // 绕过ChatOpenAI内部校验
    newFields.openAIApiKey = 'not-empty';

    // 默认为流式
    newFields.streaming = newFields.streaming ?? true;

    // 使用设置的模型
    const modelConfig = getChatModelAndConfig(newFields.modelName);
    DevConsole.log('modelConfig', modelConfig);
    newFields.modelName = modelConfig?.chatApiModel;

    //temperature获取配置
    newFields.temperature = modelConfig?.temperature === undefined ? 0.01 : modelConfig?.temperature;
    // 要透传给接口的扩展参数放在modelKwargs中
    newFields.modelKwargs = {
      ...newFields.modelKwargs,
      extParams: getExtParams(),
    };

    if (modelConfig.chatApiUrl) newFields.modelKwargs.extParams.apiUrl = modelConfig.chatApiUrl;

    newFields.onFailedAttempt = function () {
      return (error: any) => {
        const STATUS_NO_RETRY = [
          400, // Bad Request
          401, // Unauthorized
          402, // Payment Required
          403, // Forbidden
          404, // Not Found
          405, // Method Not Allowed
          406, // Not Acceptable
          407, // Proxy Authentication Required
          408, // Request Timeout
          409, // Conflict
        ];
        DevConsole?.warn('OpenAI failed attempt', { error, stack: (error as Error).stack });
        if (
          error.message.startsWith('Cancel') ||
          error.message.startsWith('AbortError') ||
          error.name === 'AbortError'
        ) {
          DevConsole?.warn('OpenAI failed because of cancel/abort', { error });
          throw error;
        }

        const status = (error as any)?.response?.status ?? (error as any)?.status;
        if (status && STATUS_NO_RETRY.includes(+status)) {
          DevConsole?.error('OpenAI failed because of status', { status });
          throw error;
        }

        if ((error as any)?.error?.code === 'insufficient_quota') {
          DevConsole?.warn('OpenAI failed because of insufficient quota', { error });
          const err = new Error(error?.message);
          err.name = 'InsufficientQuotaError';
          throw err;
        }

        if (error.code === 'ERR_STREAM_PREMATURE_CLOSE') {
          error.retry = true; // 异常重试
        }
      };
    };

    // 设置服务URL
    newFields.configuration = {
      ...newFields.configuration,
      baseURL: BASE_URL,
    };

    // 禁用默认的重试次数，默认6次
    newFields.maxRetries = newFields.maxRetries ?? 0;
    // @ts-ignore
    super(newFields);

    this.modelKwargs = newFields.modelKwargs;
  }

  /** @ignore */
  // @ts-ignore
  async _generate(
    messages: BaseMessage[],
    options: this['ParsedCallOptions'],
    runManager?: CallbackManagerForLLMRun
  ): Promise<ChatResult> {
    // 校验登录态，引导登录，更新登录参数
    const isLogin = !!hasJdhLoginCookie();
    if (!isLogin) {
      await forceJdhLogin();
      // @ts-ignore
      this.modelKwargs.extParams.jdhLoginParams = { ...getJdhLoginInfo(), sourceType: 'joyCoderFe' };
    }
    // DevConsole.log('--messages--', messages);
    // @ts-ignore
    return await super._generate(messages, options, runManager);
  }
}
