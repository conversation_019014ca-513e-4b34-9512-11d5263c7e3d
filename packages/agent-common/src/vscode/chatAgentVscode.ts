import ChatJoyCoder from './chatJoyCoder';
import chatAgent from '../chatAgent';

/**
 * 基于多agent的智能聊天
 *
 * @param task - 需要执行的任务描述。 eg.'写一个冒泡排序'
 * @param options - 附加选项，用于配置任务执行。
 * @param onProgress - 可选的进度回调函数，用于接收任务执行进度信息。
 * @returns 任务执行结果。
 */

export default function chatAgentVscode(
  task: string,
  options: Record<string, any>,
  onProgress?: (msg: string) => void
) {
  //https://js.langchain.com/v0.2/docs/integrations/chat/openai/
  const model = new ChatJoyCoder({
    temperature: 0.1,
    apiKey: 'empty',
  });
  //@ts-ignore
  return chatAgent(task, model, options, onProgress);
}
