import path from 'path';
import * as fs from 'fs';
import * as vscode from 'vscode';

/**
 * 获取文件路径。
 *
 * @param {string} fileName - 文件名。
 * @returns {object|null} 包含cssFile和tsxFile路径的对象，或null。
 */
export function saveFiles(fileList: { fileName: string; fileContent: string; fileType: string }[]) {
  if (!fileList || fileList.length == 0) {
    return [];
  }
  const files = [];
  try {
    const rootPath = vscode.workspace.workspaceFolders ? vscode.workspace.workspaceFolders[0].uri.fsPath : '';
    for (let i = 0; i < fileList.length; i++) {
      const fileName = fileList[i].fileName;
      const fileContent = fileList[i].fileContent;
      const fileType = fileList[i].fileType;
      const fileFolder = path.join(rootPath, 'src', 'demo', fileName);
      const filePath = path.join(rootPath, 'src', 'demo', fileName, `${fileName}.${fileType}`);
      const relativePath = path.join('src', 'demo', fileName, `${fileName}.${fileType}`);
      files.push({
        fileName: `${fileName}.${fileType}`,
        filePath,
        relativePath,
      });
      generateFiles(fileFolder, fileContent, filePath);
    }
    return files;
  } catch (error) {
    return files;
  }
}

/**
 * 生成文件
 * @param filePath 文件路径
 * @param content 文件内容
 * @returns Promise<boolean> 文件生成成功返回true，失败返回false
 */
export function generateFiles(fileFolder: string, content: string, filePath: string) {
  return new Promise<boolean>((resolve, reject) => {
    try {
      if (!content || !filePath) {
        resolve(false);
        return;
      }
      if (!fs.existsSync(filePath)) {
        fs.mkdirSync(fileFolder, { recursive: true });
      }
      // 创建文件
      fs.writeFile(filePath, content, (err) => {
        if (err) {
          resolve(false);
          return;
        }
        resolve(true);
      });
    } catch (error) {
      reject(false);
    }
  });
}
