import { decode } from 'html-entities';
import * as cheerio from 'cheerio';
const uaList = [
  // Chrome
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36',
  // Firefox
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/20100101 Firefox/93.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:93.0) Gecko/20100101 Firefox/93.0',
  'Mozilla/5.0 (X11; Linux x86_64; rv:93.0) Gecko/20100101 Firefox/93.0',
  // Safari
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 11_6_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15',
  // Microsoft Edge
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36 Edg/94.0.992.38',
];

export function getRandomUserAgent() {
  const randomIndex = Math.floor(Math.random() * uaList.length);
  return uaList[randomIndex];
}

export default async function search(input: string, maxResults: number): Promise<any> {
  const results: any = [];

  const headers = new Headers();
  headers.append('User-Agent', getRandomUserAgent());
  console.warn('input', input);

  const url = `https://www.baidu.com/s?f=8&ie=utf-8&rn=${maxResults}&lm=30&wd=${encodeURIComponent(input)}`;
  const resp = await fetch(url, {
    headers: headers,
  });

  const html = await resp.text();
  const respCheerio = cheerio.load(html);
  respCheerio('div.c-container.new-pmd')
    .slice(0, maxResults)
    .each((i, elem) => {
      const item = cheerio.load(elem);
      const linkElement = item('a');
      const url = (linkElement.attr('href') ?? '').trim();
      if (url !== '' && url !== '#') {
        const title = decode(linkElement.text());
        const description = item
          .text()
          .replace(/\n+\s+/g, ' ')
          .replace(/data:image\/[a-zA-Z]+;base64,[^"]+/g, '')
          .trim();
        results.push({
          title,
          content: description,
          url,
        });
      }
    });

  return results;
}
