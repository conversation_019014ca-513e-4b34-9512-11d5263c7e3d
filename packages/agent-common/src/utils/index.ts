export const isDev = process.env.NODE_ENV === 'development';

/**
 * 仅开发环境输出日志
 * @example
 * DevConsole.log('This is a log message');
 * DevConsole.warn('This is a warning message');
 * DevConsole.error('This is an error message');
 */
export class DevConsole {
  static isDevelopment = process.env.NODE_ENV === 'development';

  static log(...args: any[]) {
    if (DevConsole.isDevelopment) {
      console.log(...args);
    }
  }

  static warn(...args: any[]) {
    if (DevConsole.isDevelopment) {
      console.warn(...args);
    }
  }

  static error(...args: any[]) {
    if (DevConsole.isDevelopment) {
      console.error(...args);
    }
  }
}
