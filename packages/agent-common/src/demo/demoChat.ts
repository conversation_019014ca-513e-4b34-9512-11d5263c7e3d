import chatAgent from '../chatAgent';
import { ChatOpenAI } from '@langchain/openai';

// 运行demo需要设置好环境变量
// export OPENAI_API_KEY=xxx
//https://js.langchain.com/v0.2/docs/integrations/chat/openai/
const model = new ChatOpenAI({
  model: 'gpt-4o', //process.env.OPENAI_API_MODEL
  temperature: 0.1,
  apiKey: process.env.OPENAI_API_KEY,
  configuration: {
    baseURL: 'http://gpt-proxy.jd.com/v1/', //大模型网关
  },
});

const task = '小米SU7汽车不同车型售价是多少？';
chatAgent(task, model).then((result) => {
  console.log('result', result);
});
