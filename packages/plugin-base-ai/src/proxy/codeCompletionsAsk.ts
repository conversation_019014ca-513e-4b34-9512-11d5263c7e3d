import { sm2 } from 'sm-crypto';
import {
  forceJdhLogin,
  getJdhCgiUrl,
  getJdhLoginInfo,
  getVscodeConfig,
  ideAppName,
  ideVersion,
  isBusiness,
  modelUrl,
  PLUGIN_ID,
  isIDE,
  getBaseUrl,
  getJoyCoderVersion,
} from '@joycoder/shared';
import { v4 as uuidv4 } from 'uuid';
import fetch from 'node-fetch';

const respMaxTokens = 1000;
//模型温度。默认0.1，后续可支持配置下发或动态调整？
// const temperature = 0.1;

//code llama 续写模型
function getCodeGenerateUrl() {
  return isIDE() ? getBaseUrl() + '/api/saas/openai/v1/codeCompletions' : modelUrl + '/completion/autocompletion';
}

const showLog = process.env.NODE_ENV === 'development';
const businessCodeGenerateUrl = modelUrl + '/completion/autocompletion';
const businessChatGenerateUrl = modelUrl + '/bigdata/sendGeneralStreamSync';
/**
 * 调用Completion返回的结果
 *
 * @interface GetCodeCompletions
 */
export interface GetCodeCompletions {
  completionId?: string;
  model?: string;
  temperature?: number;
  completions: string[];
  codeOption?: object;
}
export interface OnlyBusinessParams {
  ideaVersion?: string;
  pluginVersion?: string;
  userName?: string;
  userToken?: string;
  user?: string;
  sourceType?: string;
  osName?: string;
  chatSessionId?: string;
  ide?: string;
  timestamp?: number;
}
type FetchWithTimeoutOptions = RequestInit & { timeout?: number };

export class FetchWithTimeoutController {
  static isTimeout: string | undefined = '';
  static lastAbortController: AbortController | null = null;
  static timeoutId: ReturnType<typeof setTimeout> | number | undefined = undefined; // 使用ReturnType来正确地声明timeoutId的类型
  /**
   * 使用给定的URL和选项执行带有超时的fetch请求。
   * 如果在指定的超时时间内请求未完成，则该请求将被中断。
   *
   * @param url 要请求的资源的URL。
   * @param options 包含自定义fetch选项和超时时间的对象。
   * @returns 返回一个Promise，它在请求成功时解析为Response对象。
   */
  static async fetchWithTimeout(url: string, options: FetchWithTimeoutOptions): Promise<Response> {
    // 清除上一个请求（如果存在且未被取消）
    if (this.lastAbortController) {
      this.abortPreviousRequest();
    }
    // 为当前请求创建一个新的AbortController
    const currentAbortController = new AbortController();
    this.lastAbortController = currentAbortController;
    const { signal } = currentAbortController;
    const { timeout = 25000, ...fetchOptions } = options;

    try {
      // 设置超时逻辑
      this.timeoutId = setTimeout(() => {
        this.abortPreviousRequest('isTimeout');
      }, timeout);
      const response: any = await fetch(url, { ...fetchOptions, signal } as any);
      this.isTimeout = '';
      clearTimeout(this.timeoutId); // 请求成功，清除超时定时器
      return response;
    } catch (error) {
      clearTimeout(this.timeoutId); // 请求成功，清除超时定时器
      throw error;
    } finally {
      // 请求结束（无论成功或失败），清除定时器
      // 如果当前请求是最后一个请求，清除lastAbortController引用
      if (currentAbortController === this.lastAbortController) {
        this.lastAbortController = null;
      }
      if (this.timeoutId !== null) {
        clearTimeout(this.timeoutId);
        this.timeoutId = undefined;
      }
    }
  }

  /**
   * 如果存在上一个请求且未被取消，则取消它。
   */
  private static abortPreviousRequest(type?: string) {
    this.isTimeout = type;
    if (this.lastAbortController && !this.lastAbortController.signal.aborted) {
      this.lastAbortController.abort();
      this.lastAbortController = null; // 清除引用，表示没有正在进行的请求
    }
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
      this.timeoutId = undefined;
    }
  }
}

export function fetchWithTimeout(url: string, options: RequestInit, timeout = 10000): Promise<Response> {
  const fetchPromise = FetchWithTimeoutController.fetchWithTimeout(url, { ...options, timeout });
  return fetchPromise;
}

/**
 * 客户端调用Code-Llama模型，该函数用于向客户端GPT提问，并返回一个包含字符串数组的Promise。
 * @returns 一个Promise，包含一个字符串数组
 */
export function askClientCode(
  prefix_code = '',
  suffix_code = '',
  language = '',
  file_path = '',
  project_name = '',
  related_files = '',
  maxLines = 1,
  model = 'code-llama',
  codeOption = {
    url: '',
  }
): Promise<GetCodeCompletions> {
  return new Promise((resolve) => {
    const {
      codeUrl,
      max_tokens,
      max_times,
      temperature,
      onlyBusinessParams,
      gen_task = 'COMPLETE_CODE_N_LINE',
    } = getBusinessOrInner(codeOption);

    let rawOption = {
      temperature,
      max_tokens,
      model: model,
      ide: ideAppName,
      // user: getJdhLoginInfo()?.erp || '',
      ...onlyBusinessParams,
      related_files,
      max_times,
      prefix_lines: 500,
      suffix_lines: 100,
      max_lines: maxLines, //最大行数
      gen_task,
      language: language,
      prefix_code,
      suffix_code,
      file_path,
      filePath: file_path,
      project_name,
      url: '',
      codeLanguage: language,
      prefixCode: prefix_code,
      suffixCode: suffix_code,
      sufixCode: suffix_code,
      maxLines: maxLines,
    };
    rawOption = Object.assign(rawOption, codeOption);
    //无需url
    rawOption.url = '';
    console.log('补全接口请求入参rawOption', rawOption);

    if (isIDE()) {
      const encryptData = sm2.doEncrypt(JSON.stringify(rawOption), getJdhLoginInfo()?.pk, 1);
      rawOption = {
        ...rawOption,
        enData: encryptData,
      };
    }

    const raw: string = JSON.stringify(rawOption);

    const headers = getFetchHeader();
    const requestOptions: RequestInit = {
      method: 'POST',
      headers,
      body: raw,
      redirect: 'follow',
    };
    showLog && console.warn('raw1', codeUrl, raw);
    FetchWithTimeoutController.fetchWithTimeout(codeUrl, requestOptions)
      .then((response) => response.json())
      .then((data) => {
        if (data?.code == '1008') {
          forceJdhLogin();
          resolve({
            completionId: '',
            model: model,
            temperature,
            completions: [''],
          });
          return;
        }
        const resChoices = data?.choices || data?.data?.choices || [];
        const resData = resChoices.map((resItem) => resItem?.text || '');
        console.log('服务端返回', resData);
        showLog && console.log('res1', JSON.stringify(resData));
        resolve({
          completionId: data?.id || data?.data?.id || '',
          model: model,
          temperature,
          completions: resData,
        });
      })
      .catch((error) => {
        console.log('服务端返回失败', error);
        console.warn('Error:', error);
        resolve({
          completionId: '',
          model: model,
          temperature,
          completions: [''],
        });
      });
  });
}

/**
 * 客户端调用Code-Llama模型，写注释，或者通过注释写代码
 * @returns 一个Promise，包含一个字符串数组
 */
export function askClientChat(promptStr, modelOption): Promise<GetCodeCompletions> {
  return new Promise((resolve) => {
    const chatOptionConfig = modelOption?.chat || {};

    let chatOption = {
      model: '', //聊天模型
      url: '', //聊天模型地址
      temperature: 0.1,
      max_tokens: 2048,
    };
    chatOption = Object.assign(chatOption, chatOptionConfig);
    const { chatUrl, max_tokens, temperature, onlyChatBusinessParams } = getBusinessOrInner(chatOption);

    let reqData = {
      temperature: temperature ?? chatOption.temperature,
      max_tokens: max_tokens ?? respMaxTokens,
      model: chatOption.model,
      ...onlyChatBusinessParams,
      messages: [
        {
          role: 'user',
          content: promptStr,
        },
      ],
    };

    if (isIDE()) {
      const encryptData = sm2.doEncrypt(JSON.stringify(reqData), getJdhLoginInfo()?.pk, 1);
      reqData = {
        ...reqData,
        enData: encryptData,
      };
    }

    const raw: string = JSON.stringify(reqData);
    const headers = getFetchHeader();
    const requestOptions: RequestInit = {
      method: 'POST',
      headers,
      body: raw,
      redirect: 'follow',
    };
    showLog && console.warn('raw2', raw);

    FetchWithTimeoutController.fetchWithTimeout(chatUrl, requestOptions)
      .then((response) => response.json())
      .then((data) => {
        if (isBusiness() && data?.code == '1008') {
          forceJdhLogin();
          resolve({
            completionId: '',
            model: chatOption.model,
            temperature,
            completions: [''],
          });
          return;
        }
        showLog && console.warn('res2', JSON.stringify(data));
        const dataStr = String(data);
        let text = '';
        // 返回数据命中敏感词
        if (dataStr == '[INVALID_RESP]') {
          text = '返回数据中包含敏感信息~';
        } else {
          text = data?.choices?.[0]?.message?.content || '';
        }
        resolve({
          completionId: data?.id || '',
          model: chatOption.model,
          temperature: chatOption.temperature,
          completions: [text],
        });
      })
      .catch((error) => {
        console.warn('Error:', error);
        if (error.name !== 'AbortError' || FetchWithTimeoutController.isTimeout == 'isTimeout') {
          resolve({
            completionId: '',
            model: chatOption.model,
            temperature: chatOption.temperature,
            completions: [''],
          });
        }
      });
  });
}

export function getBusinessOrInner(codeOption) {
  // let codeUrl: string = codeOption?.url || getCodeGenerateUrl();
  let codeUrl: string = getCodeGenerateUrl();
  let chatUrl = codeOption.url;
  let max_tokens = respMaxTokens;
  const max_times = getVscodeConfig('JoyCode.config.codeCompletionsMaxTimes') ?? 2000;
  const taskMap = {
    LINE: 'COMPLETE_CODE_N_LINE',
    BLOCK: 'COMPLETE_CODE_BLOCK',
    FUNCTION: 'COMPLETE_CODE_FUNCTION',
    TIME_OUT: 'COMPLETE_TIME_OUT',
  };
  const gen_task = taskMap[getVscodeConfig('JoyCode.config.codeCompletionsGenTask') ?? 'LINE'];
  let temperature = 0.1;
  let onlyBusinessParams: OnlyBusinessParams = {
    user: getJdhLoginInfo()?.erp || '',
    userToken: getJdhLoginInfo()?.userToken,
    userName: getJdhLoginInfo()?.userName || '',
  };
  let onlyChatBusinessParams: OnlyBusinessParams = {
    user: getJdhLoginInfo()?.erp || '',
    ide: ideAppName,
    osName: ideAppName,
    ideaVersion: isIDE() ? getJoyCoderVersion() : ideVersion,
    pluginVersion: PLUGIN_ID,
    userToken: getJdhLoginInfo()?.userToken,
    userName: getJdhLoginInfo()?.userName || '',
  };
  if (isBusiness()) {
    codeUrl = getJdhCgiUrl(codeOption?.url || businessCodeGenerateUrl);
    chatUrl = getJdhCgiUrl(codeOption.url || businessChatGenerateUrl);
    max_tokens = getVscodeConfig('JoyCode.config.codeCompletionsMaxTokens') || 1000;
    temperature = getVscodeConfig('JoyCode.config.codeCompletionsTemperature') || 0;
    onlyBusinessParams = {
      sourceType: 'open', //   - 商业化版本
    };
    onlyChatBusinessParams = {
      timestamp: new Date().getTime(),
      chatSessionId: uuidv4() + getJdhLoginInfo()?.userName,
      sourceType: 'open', //   - 商业化版本
    };
  }
  return {
    chatUrl,
    codeUrl,
    max_times,
    gen_task,
    max_tokens,
    temperature,
    onlyBusinessParams,
    onlyChatBusinessParams,
  };
}

function getFetchHeader() {
  const baseHeaders = {
    'Content-Type': 'application/json; charset=UTF-8',
  };
  const jdhLoginInfo = getJdhLoginInfo();
  const headers =
    isIDE() && jdhLoginInfo?.ptKey
      ? {
          ...baseHeaders,
          ptKey: jdhLoginInfo.ptKey,
          ...(jdhLoginInfo?.loginType && { loginType: jdhLoginInfo?.loginType }),
        }
      : baseHeaders;
  return headers;
}
