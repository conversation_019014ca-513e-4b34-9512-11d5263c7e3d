import { parseTreeSitter } from './elidableText';

/**
 * 使用 Tree-sitter 解析代码，提取导入语句中的路径。
 * @param language 代码所使用的编程语言
 * @param code 要解析的源代码字符串
 * @returns 包含导入路径的字符串数组
 */
export async function extractImports(language, code) {
  const tree = await parseTreeSitter(language, code);
  try {
    const importNodes: any = [];

    function walk(node) {
      if (node.type === 'import_statement') {
        importNodes.push(node);
      }
      node.children.forEach(walk);
    }

    walk(tree.rootNode);

    const importPaths = importNodes.map((node) => {
      const stringNode = node.children.find((child) => child.type === 'string');
      return stringNode ? stringNode.text.slice(1, -1) : '';
    });
    return importPaths;
  } finally {
    tree.delete();
  }
}

// // 示例代码
// const code = `
// import React from 'react';
// import { render } from 'react-dom';
// import App from './App';
// `;

// extractImports('javascript',code).then((imports) => {
//   console.log('Imported files:', imports);
// });

//输出：
// ['react', 'react-dom', './App']
