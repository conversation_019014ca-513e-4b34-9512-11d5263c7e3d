// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "内部版本",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
        // "--disable-extensions"
			],
      "env": {
        "NODE_ENV": "development",
      },
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "${defaultBuildTask}",
      "skipFiles": [
        "<node_internals>/**"
      ]
		},
    {
			"name": "内部版本(禁用其他插件)",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
        "--disable-extensions"
			],
      "env": {
        "NODE_ENV": "development",
      },
			"outFiles": [
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "${defaultBuildTask}"
		},
    {
			"name": "IDE版本",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-IDE",
			],
      "env": {
        "NODE_ENV": "development",
      },
			"outFiles": [
				"${workspaceFolder}/packages/vscode-IDE/dist/**/*.js"
			],
      "sourceMapPathOverrides": {
        "webpack://joycoder-editor/*": "${workspaceFolder}/packages/vscode-IDE/*",
      },
			"preLaunchTask": "npm: watch-ide"
		},
    {
			"name": "IDE版本(禁用其他插件)",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-IDE",
        "--disable-extensions"
			],
      "env": {
        "NODE_ENV": "development",
      },
			"outFiles": [
				"${workspaceFolder}/packages/vscode-IDE/dist/**/*.js"
			],
      "sourceMapPathOverrides": {
        "webpack://joycoder-editor/*": "${workspaceFolder}/packages/vscode-IDE/*",
      },
			"preLaunchTask": "npm: watch-ide"
		},
    {
			"name": "商业化版本",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-business",
        // "--disable-extensions"
			],
      "env": {
        "NODE_ENV": "development",
      },
			"outFiles": [
				"${workspaceFolder}/packages/vscode-business/dist/**/*.js"
			],
      "sourceMapPathOverrides": {
        "webpack://joycoder-business/*": "${workspaceFolder}/packages/vscode-business/*",
      },
			"preLaunchTask": "npm: watch-business"
		},
    {
			"name": "商业化版本(禁用其他插件)",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-business",
        "--disable-extensions"
			],
      "env": {
        "NODE_ENV": "development",
      },
			"outFiles": [
				"${workspaceFolder}/packages/vscode-business/dist/**/*.js"
			],
      "sourceMapPathOverrides": {
        "webpack://joycoder-business/*": "${workspaceFolder}/packages/vscode-business/*",
      },
			"preLaunchTask": "npm: watch-business"
		},

		{
			"name": "商业化版本-本地化部署",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-business",
        // "--disable-extensions"
			],
      "env": {
        "NODE_ENV": "development",
      },
			"outFiles": [
				"${workspaceFolder}/packages/vscode-business/dist/**/*.js"
			],
      "sourceMapPathOverrides": {
        "webpack://joycoder-business/*": "${workspaceFolder}/packages/vscode-business/*",
      },
			"preLaunchTask": "npm: watch-business-local"
		},
    {
			"name": "商业化版本-本地化部署(禁用其他插件)",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/packages/vscode-business",
        "--disable-extensions"
			],
      "env": {
        "NODE_ENV": "development",
      },
			"outFiles": [
				"${workspaceFolder}/packages/vscode-business/dist/**/*.js"
			],
      "sourceMapPathOverrides": {
        "webpack://joycoder-business/*": "${workspaceFolder}/packages/vscode-business/*",
      },
			"preLaunchTask": "npm: watch-business-local"
		},

		{
			"name": "Extension Tests",
			"type": "extensionHost",
			"request": "launch",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}",
				"--extensionTestsPath=${workspaceFolder}/out/test/suite/index"
			],
			"outFiles": [
				"${workspaceFolder}/out/**/*.js",
				"${workspaceFolder}/dist/**/*.js"
			],
			"preLaunchTask": "tasks: watch-tests"
		}
	]
}
